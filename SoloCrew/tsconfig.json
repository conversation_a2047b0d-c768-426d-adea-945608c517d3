{"extends": "expo/tsconfig.base", "compilerOptions": {"target": "esnext", "lib": ["dom", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "allowSyntheticDefaultImports": true}, "include": ["**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}