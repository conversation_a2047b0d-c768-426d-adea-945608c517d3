import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  Animated,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useNavigation } from '@react-navigation/native';
import { Picker } from '@react-native-picker/picker';
import * as Haptics from 'expo-haptics';

import { COLORS, ANIMATION_DURATION, GENDER_OPTIONS, ACTIVITY_TYPES } from '../constants';
import { RegisterFormData, ActivityType } from '../types';
import { 
  validateEmail, 
  validatePassword, 
  validateName, 
  validateConfirmPassword,
  createFormField, 
  updateFormField 
} from '../utils';

const RegisterScreen: React.FC = () => {
  const navigation = useNavigation();
  const [isLoading, setIsLoading] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  
  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: createFormField(),
    email: createFormField(),
    password: createFormField(),
    confirmPassword: createFormField(),
    age: createFormField(),
    gender: createFormField(),
    city: createFormField(),
    preferredActivities: [],
  });

  React.useEffect(() => {
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: ANIMATION_DURATION.NORMAL,
      useNativeDriver: true,
    }).start();
  }, []);

  const handleInputChange = (field: keyof RegisterFormData, value: string) => {
    let error: string | undefined;
    
    switch (field) {
      case 'firstName':
        error = validateName(value);
        break;
      case 'email':
        error = validateEmail(value);
        break;
      case 'password':
        error = validatePassword(value);
        break;
      case 'confirmPassword':
        error = validateConfirmPassword(formData.password.value, value);
        break;
      case 'age':
        const ageNum = parseInt(value);
        if (isNaN(ageNum) || ageNum < 18 || ageNum > 100) {
          error = 'Age must be between 18 and 100';
        }
        break;
      case 'city':
        error = validateName(value);
        break;
    }

    setFormData(prev => ({
      ...prev,
      [field]: updateFormField(prev[field], value, error),
    }));
  };

  const handleActivityToggle = (activity: ActivityType) => {
    setFormData(prev => ({
      ...prev,
      preferredActivities: prev.preferredActivities.includes(activity)
        ? prev.preferredActivities.filter(a => a !== activity)
        : [...prev.preferredActivities, activity],
    }));
  };

  const isFormValid = () => {
    const fieldsValid = Object.entries(formData).every(([key, field]) => {
      if (key === 'preferredActivities') return true;
      return !field.error && field.value.trim() !== '';
    });
    
    return fieldsValid && formData.preferredActivities.length > 0;
  };

  const handleRegister = async () => {
    if (!isFormValid()) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Invalid Input', 'Please fill in all fields correctly.');
      return;
    }

    setIsLoading(true);
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    try {
      // TODO: Implement actual registration API call
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert('Success!', 'Account created successfully. Welcome to SoloCrew!', [
        { text: 'Continue', onPress: () => navigation.navigate('Main' as never) }
      ]);
    } catch (error) {
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Registration Failed', 'Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoginPress = () => {
    navigation.navigate('Login' as never);
  };

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardAvoidingView}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          <Animated.View style={[styles.content, { opacity: fadeAnim }]}>
            {/* Header */}
            <View style={styles.header}>
              <Text style={styles.title}>Join SoloCrew</Text>
              <Text style={styles.subtitle}>Create your account to start meeting people</Text>
            </View>

            {/* Form */}
            <View style={styles.form}>
              {/* Basic Info */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>First Name</Text>
                <TextInput
                  style={[
                    styles.input,
                    formData.firstName.error && formData.firstName.touched && styles.inputError,
                  ]}
                  placeholder="Enter your first name"
                  placeholderTextColor={COLORS.gray[400]}
                  value={formData.firstName.value}
                  onChangeText={(value) => handleInputChange('firstName', value)}
                  autoCapitalize="words"
                />
                {formData.firstName.error && formData.firstName.touched && (
                  <Text style={styles.errorText}>{formData.firstName.error}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Email</Text>
                <TextInput
                  style={[
                    styles.input,
                    formData.email.error && formData.email.touched && styles.inputError,
                  ]}
                  placeholder="Enter your email"
                  placeholderTextColor={COLORS.gray[400]}
                  value={formData.email.value}
                  onChangeText={(value) => handleInputChange('email', value)}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {formData.email.error && formData.email.touched && (
                  <Text style={styles.errorText}>{formData.email.error}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Password</Text>
                <TextInput
                  style={[
                    styles.input,
                    formData.password.error && formData.password.touched && styles.inputError,
                  ]}
                  placeholder="Create a password"
                  placeholderTextColor={COLORS.gray[400]}
                  value={formData.password.value}
                  onChangeText={(value) => handleInputChange('password', value)}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {formData.password.error && formData.password.touched && (
                  <Text style={styles.errorText}>{formData.password.error}</Text>
                )}
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>Confirm Password</Text>
                <TextInput
                  style={[
                    styles.input,
                    formData.confirmPassword.error && formData.confirmPassword.touched && styles.inputError,
                  ]}
                  placeholder="Confirm your password"
                  placeholderTextColor={COLORS.gray[400]}
                  value={formData.confirmPassword.value}
                  onChangeText={(value) => handleInputChange('confirmPassword', value)}
                  secureTextEntry
                  autoCapitalize="none"
                  autoCorrect={false}
                />
                {formData.confirmPassword.error && formData.confirmPassword.touched && (
                  <Text style={styles.errorText}>{formData.confirmPassword.error}</Text>
                )}
              </View>

              {/* Personal Info */}
              <View style={styles.row}>
                <View style={[styles.inputContainer, styles.halfWidth]}>
                  <Text style={styles.label}>Age</Text>
                  <TextInput
                    style={[
                      styles.input,
                      formData.age.error && formData.age.touched && styles.inputError,
                    ]}
                    placeholder="Age"
                    placeholderTextColor={COLORS.gray[400]}
                    value={formData.age.value}
                    onChangeText={(value) => handleInputChange('age', value)}
                    keyboardType="numeric"
                  />
                  {formData.age.error && formData.age.touched && (
                    <Text style={styles.errorText}>{formData.age.error}</Text>
                  )}
                </View>

                <View style={[styles.inputContainer, styles.halfWidth]}>
                  <Text style={styles.label}>Gender</Text>
                  <View style={styles.pickerContainer}>
                    <Picker
                      selectedValue={formData.gender.value}
                      onValueChange={(value) => handleInputChange('gender', value)}
                      style={styles.picker}
                    >
                      <Picker.Item label="Select gender" value="" />
                      {GENDER_OPTIONS.map((option) => (
                        <Picker.Item key={option.value} label={option.label} value={option.value} />
                      ))}
                    </Picker>
                  </View>
                </View>
              </View>

              <View style={styles.inputContainer}>
                <Text style={styles.label}>City</Text>
                <TextInput
                  style={[
                    styles.input,
                    formData.city.error && formData.city.touched && styles.inputError,
                  ]}
                  placeholder="Enter your city"
                  placeholderTextColor={COLORS.gray[400]}
                  value={formData.city.value}
                  onChangeText={(value) => handleInputChange('city', value)}
                  autoCapitalize="words"
                />
                {formData.city.error && formData.city.touched && (
                  <Text style={styles.errorText}>{formData.city.error}</Text>
                )}
              </View>

              {/* Preferred Activities */}
              <View style={styles.inputContainer}>
                <Text style={styles.label}>Preferred Activities</Text>
                <Text style={styles.sublabel}>Select activities you're interested in</Text>
                <View style={styles.activitiesContainer}>
                  {ACTIVITY_TYPES.map((activity) => (
                    <TouchableOpacity
                      key={activity.value}
                      style={[
                        styles.activityChip,
                        formData.preferredActivities.includes(activity.value) && styles.activityChipSelected,
                      ]}
                      onPress={() => handleActivityToggle(activity.value)}
                      activeOpacity={0.7}
                    >
                      <Text style={styles.activityEmoji}>{activity.emoji}</Text>
                      <Text
                        style={[
                          styles.activityText,
                          formData.preferredActivities.includes(activity.value) && styles.activityTextSelected,
                        ]}
                      >
                        {activity.label}
                      </Text>
                    </TouchableOpacity>
                  ))}
                </View>
              </View>

              <TouchableOpacity
                style={[
                  styles.registerButton,
                  (!isFormValid() || isLoading) && styles.registerButtonDisabled,
                ]}
                onPress={handleRegister}
                disabled={!isFormValid() || isLoading}
                activeOpacity={0.8}
              >
                <Text style={styles.registerButtonText}>
                  {isLoading ? 'Creating Account...' : 'Create Account'}
                </Text>
              </TouchableOpacity>
            </View>

            {/* Footer */}
            <View style={styles.footer}>
              <Text style={styles.footerText}>Already have an account? </Text>
              <TouchableOpacity onPress={handleLoginPress} activeOpacity={0.7}>
                <Text style={styles.loginText}>Sign In</Text>
              </TouchableOpacity>
            </View>
          </Animated.View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.white,
  },
  keyboardAvoidingView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 40,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: COLORS.gray[900],
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: COLORS.gray[600],
  },
  form: {
    flex: 1,
    gap: 20,
  },
  inputContainer: {
    gap: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.gray[700],
  },
  sublabel: {
    fontSize: 14,
    color: COLORS.gray[500],
  },
  input: {
    height: 56,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    borderRadius: 12,
    paddingHorizontal: 16,
    fontSize: 16,
    backgroundColor: COLORS.white,
  },
  inputError: {
    borderColor: COLORS.error,
  },
  errorText: {
    fontSize: 14,
    color: COLORS.error,
    marginTop: 4,
  },
  row: {
    flexDirection: 'row',
    gap: 16,
  },
  halfWidth: {
    flex: 1,
  },
  pickerContainer: {
    height: 56,
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    borderRadius: 12,
    backgroundColor: COLORS.white,
    justifyContent: 'center',
  },
  picker: {
    height: 56,
  },
  activitiesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginTop: 8,
  },
  activityChip: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 24,
    backgroundColor: COLORS.gray[100],
    borderWidth: 2,
    borderColor: COLORS.gray[200],
    gap: 8,
  },
  activityChipSelected: {
    backgroundColor: COLORS.primary[50],
    borderColor: COLORS.primary[300],
  },
  activityEmoji: {
    fontSize: 16,
  },
  activityText: {
    fontSize: 14,
    fontWeight: '500',
    color: COLORS.gray[700],
  },
  activityTextSelected: {
    color: COLORS.primary[700],
  },
  registerButton: {
    height: 56,
    backgroundColor: COLORS.primary[600],
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    shadowColor: COLORS.primary[600],
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 4,
  },
  registerButtonDisabled: {
    backgroundColor: COLORS.gray[300],
    shadowOpacity: 0,
    elevation: 0,
  },
  registerButtonText: {
    color: COLORS.white,
    fontSize: 18,
    fontWeight: '600',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 24,
  },
  footerText: {
    fontSize: 16,
    color: COLORS.gray[600],
  },
  loginText: {
    fontSize: 16,
    fontWeight: '600',
    color: COLORS.primary[600],
  },
});

export default RegisterScreen;
