import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { COLORS } from '../constants';

const SessionsHistoryScreen: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>History</Text>
        <View style={styles.placeholder}>
          <Text style={styles.placeholderText}>📅</Text>
          <Text style={styles.placeholderSubtext}>Your session history will appear here</Text>
        </View>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: COLORS.white },
  content: { flex: 1, paddingHorizontal: 24, paddingTop: 20 },
  title: { fontSize: 28, fontWeight: 'bold', color: COLORS.gray[900], marginBottom: 32 },
  placeholder: { flex: 1, justifyContent: 'center', alignItems: 'center' },
  placeholderText: { fontSize: 48, marginBottom: 16 },
  placeholderSubtext: { fontSize: 18, color: COLORS.gray[500], textAlign: 'center' },
});

export default SessionsHistoryScreen;
