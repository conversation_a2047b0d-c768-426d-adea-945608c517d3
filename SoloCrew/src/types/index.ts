// User Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  username?: string;
  age: number;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  city: string;
  preferredActivities: ActivityType[];
  avatar?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserRegistration {
  firstName: string;
  email: string;
  password: string;
  age: number;
  gender: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  city: string;
  preferredActivities: ActivityType[];
}

export interface UserLogin {
  email: string;
  password: string;
}

// Session Types
export type ActivityType = 'drinks' | 'coffee' | 'walk' | 'sports' | 'dinner' | 'movies' | 'hiking' | 'gaming' | 'art' | 'music';

export interface SocialSession {
  id: string;
  title: string;
  description: string;
  activityType: ActivityType;
  date: string;
  time: string;
  location: {
    name: string;
    address: string;
    latitude?: number;
    longitude?: number;
  };
  maxParticipants: number;
  currentParticipants: number;
  city: string;
  isActive: boolean;
  registrationDeadline: string;
  createdAt: string;
  updatedAt: string;
}

export interface SessionRegistration {
  sessionId: string;
  userId: string;
  registeredAt: string;
  status: 'registered' | 'confirmed' | 'cancelled';
}

// Group Types
export interface Group {
  id: string;
  sessionId: string;
  members: GroupMember[];
  meetingLocation: {
    name: string;
    address: string;
    latitude?: number;
    longitude?: number;
  };
  meetingTime: string;
  status: 'forming' | 'confirmed' | 'completed' | 'cancelled';
  chatId: string;
  createdAt: string;
  updatedAt: string;
}

export interface GroupMember {
  id: string;
  userId: string;
  firstName: string;
  avatar?: string;
  joinedAt: string;
  status: 'pending' | 'confirmed' | 'declined';
}

// Chat Types
export interface ChatMessage {
  id: string;
  groupId: string;
  senderId: string;
  senderName: string;
  senderAvatar?: string;
  content: string;
  timestamp: string;
  messageType: 'text' | 'image' | 'system';
  status: 'sent' | 'delivered' | 'read';
}

export interface Chat {
  id: string;
  groupId: string;
  messages: ChatMessage[];
  participants: ChatParticipant[];
  lastMessage?: ChatMessage;
  updatedAt: string;
}

export interface ChatParticipant {
  userId: string;
  firstName: string;
  avatar?: string;
  lastReadMessageId?: string;
  isOnline: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Navigation Types
export type RootStackParamList = {
  Onboarding: undefined;
  Auth: undefined;
  Main: undefined;
};

export type AuthStackParamList = {
  Login: undefined;
  Register: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Groups: undefined;
  Profile: undefined;
  History: undefined;
};

export type HomeStackParamList = {
  Dashboard: undefined;
  SessionDetails: { sessionId: string };
  GroupDetails: { groupId: string };
  Chat: { groupId: string; chatId: string };
};

// Component Props Types
export interface SessionCardProps {
  session: SocialSession;
  onJoin: (sessionId: string) => void;
  isJoined: boolean;
  isLoading?: boolean;
}

export interface GroupCardProps {
  group: Group;
  onPress: () => void;
}

export interface MessageBubbleProps {
  message: ChatMessage;
  isOwnMessage: boolean;
  showAvatar?: boolean;
}

// Form Types
export interface FormField {
  value: string;
  error?: string;
  touched: boolean;
}

export interface LoginFormData {
  email: FormField;
  password: FormField;
}

export interface RegisterFormData {
  firstName: FormField;
  email: FormField;
  password: FormField;
  confirmPassword: FormField;
  age: FormField;
  gender: FormField;
  city: FormField;
  preferredActivities: ActivityType[];
}

// Store Types
export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface SessionsState {
  sessions: SocialSession[];
  userSessions: SocialSession[];
  isLoading: boolean;
  error: string | null;
  lastFetch: string | null;
}

export interface GroupsState {
  groups: Group[];
  currentGroup: Group | null;
  isLoading: boolean;
  error: string | null;
}

export interface ChatState {
  chats: { [groupId: string]: Chat };
  activeChat: string | null;
  isLoading: boolean;
  error: string | null;
}

// Utility Types
export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

export interface NetworkState {
  isConnected: boolean;
  isInternetReachable: boolean;
}

// Animation Types
export interface AnimationConfig {
  duration: number;
  easing?: 'linear' | 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out';
  delay?: number;
}

// Notification Types
export interface PushNotification {
  id: string;
  title: string;
  body: string;
  data?: any;
  timestamp: string;
}

export interface NotificationSettings {
  groupMessages: boolean;
  sessionReminders: boolean;
  groupFormation: boolean;
  marketing: boolean;
}
