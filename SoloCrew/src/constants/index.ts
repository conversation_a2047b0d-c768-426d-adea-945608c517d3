import { ActivityType } from '../types';

// API Configuration
export const API_CONFIG = {
  BASE_URL: __DEV__ ? 'http://localhost:8000/api' : 'https://api.solocrew.com',
  TIMEOUT: 10000,
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000,
};

// Authentication
export const AUTH_CONFIG = {
  TOKEN_KEY: '@solocrew_token',
  REFRESH_TOKEN_KEY: '@solocrew_refresh_token',
  USER_KEY: '@solocrew_user',
  TOKEN_EXPIRY_BUFFER: 5 * 60 * 1000, // 5 minutes in milliseconds
};

// Activity Types and Labels
export const ACTIVITY_TYPES: { value: ActivityType; label: string; emoji: string }[] = [
  { value: 'drinks', label: 'Drinks & Bar', emoji: '🍻' },
  { value: 'coffee', label: 'Coffee & Chat', emoji: '☕' },
  { value: 'walk', label: 'Walk & Talk', emoji: '🚶' },
  { value: 'sports', label: 'Sports & Games', emoji: '⚽' },
  { value: 'dinner', label: 'Dinner & Food', emoji: '🍽️' },
  { value: 'movies', label: 'Movies & Cinema', emoji: '🎬' },
  { value: 'hiking', label: 'Hiking & Nature', emoji: '🥾' },
  { value: 'gaming', label: 'Gaming & Tech', emoji: '🎮' },
  { value: 'art', label: 'Art & Culture', emoji: '🎨' },
  { value: 'music', label: 'Music & Concerts', emoji: '🎵' },
];

// Gender Options
export const GENDER_OPTIONS = [
  { value: 'male', label: 'Male' },
  { value: 'female', label: 'Female' },
  { value: 'other', label: 'Other' },
  { value: 'prefer_not_to_say', label: 'Prefer not to say' },
] as const;

// App Colors (matching Tailwind config)
export const COLORS = {
  primary: {
    50: '#f0f9ff',
    100: '#e0f2fe',
    200: '#bae6fd',
    300: '#7dd3fc',
    400: '#38bdf8',
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
    800: '#075985',
    900: '#0c4a6e',
  },
  secondary: {
    50: '#fdf4ff',
    100: '#fae8ff',
    200: '#f5d0fe',
    300: '#f0abfc',
    400: '#e879f9',
    500: '#d946ef',
    600: '#c026d3',
    700: '#a21caf',
    800: '#86198f',
    900: '#701a75',
  },
  gray: {
    50: '#f9fafb',
    100: '#f3f4f6',
    200: '#e5e7eb',
    300: '#d1d5db',
    400: '#9ca3af',
    500: '#6b7280',
    600: '#4b5563',
    700: '#374151',
    800: '#1f2937',
    900: '#111827',
  },
  success: '#10b981',
  warning: '#f59e0b',
  error: '#ef4444',
  white: '#ffffff',
  black: '#000000',
};

// Animation Durations
export const ANIMATION_DURATION = {
  FAST: 200,
  NORMAL: 300,
  SLOW: 500,
  VERY_SLOW: 800,
};

// Screen Dimensions
export const SCREEN_PADDING = {
  HORIZONTAL: 16,
  VERTICAL: 20,
};

// Form Validation
export const VALIDATION_RULES = {
  EMAIL_REGEX: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PASSWORD_MIN_LENGTH: 8,
  NAME_MIN_LENGTH: 2,
  NAME_MAX_LENGTH: 50,
  AGE_MIN: 18,
  AGE_MAX: 100,
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network connection failed. Please check your internet connection.',
  UNAUTHORIZED: 'Your session has expired. Please log in again.',
  VALIDATION_ERROR: 'Please check your input and try again.',
  GENERIC_ERROR: 'Something went wrong. Please try again.',
  EMAIL_INVALID: 'Please enter a valid email address.',
  PASSWORD_TOO_SHORT: `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters long.`,
  NAME_TOO_SHORT: `Name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters long.`,
  NAME_TOO_LONG: `Name must be less than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters long.`,
  AGE_INVALID: `Age must be between ${VALIDATION_RULES.AGE_MIN} and ${VALIDATION_RULES.AGE_MAX}.`,
  PASSWORDS_DONT_MATCH: 'Passwords do not match.',
  REQUIRED_FIELD: 'This field is required.',
};

// Success Messages
export const SUCCESS_MESSAGES = {
  REGISTRATION_SUCCESS: 'Account created successfully! Welcome to SoloCrew.',
  LOGIN_SUCCESS: 'Welcome back!',
  SESSION_JOINED: 'Successfully joined the session!',
  MESSAGE_SENT: 'Message sent successfully.',
  PROFILE_UPDATED: 'Profile updated successfully.',
};

// Notification Types
export const NOTIFICATION_TYPES = {
  GROUP_MESSAGE: 'group_message',
  SESSION_REMINDER: 'session_reminder',
  GROUP_FORMATION: 'group_formation',
  SESSION_UPDATE: 'session_update',
} as const;

// Chat Configuration
export const CHAT_CONFIG = {
  MESSAGE_LIMIT: 50,
  TYPING_TIMEOUT: 3000,
  RECONNECT_INTERVAL: 5000,
  MAX_MESSAGE_LENGTH: 1000,
};

// Session Configuration
export const SESSION_CONFIG = {
  MAX_PARTICIPANTS: 8,
  MIN_PARTICIPANTS: 3,
  REGISTRATION_DEADLINE_HOURS: 24,
  GROUP_FORMATION_DELAY_HOURS: 2,
};

// Storage Keys
export const STORAGE_KEYS = {
  ONBOARDING_COMPLETED: '@solocrew_onboarding_completed',
  NOTIFICATION_SETTINGS: '@solocrew_notification_settings',
  THEME_PREFERENCE: '@solocrew_theme_preference',
  LAST_SYNC: '@solocrew_last_sync',
};

// Default Values
export const DEFAULT_VALUES = {
  NOTIFICATION_SETTINGS: {
    groupMessages: true,
    sessionReminders: true,
    groupFormation: true,
    marketing: false,
  },
  PAGINATION: {
    LIMIT: 20,
    PAGE: 1,
  },
};

// App Information
export const APP_INFO = {
  NAME: 'SoloCrew',
  VERSION: '1.0.0',
  TAGLINE: "You're alone? We'll form you a Crew.",
  SUPPORT_EMAIL: '<EMAIL>',
  PRIVACY_POLICY_URL: 'https://solocrew.com/privacy',
  TERMS_OF_SERVICE_URL: 'https://solocrew.com/terms',
};

// Feature Flags
export const FEATURE_FLAGS = {
  ENABLE_PUSH_NOTIFICATIONS: true,
  ENABLE_LOCATION_SERVICES: true,
  ENABLE_HAPTIC_FEEDBACK: true,
  ENABLE_ANALYTICS: true,
  ENABLE_CRASH_REPORTING: true,
};

// Development Configuration
export const DEV_CONFIG = {
  ENABLE_FLIPPER: __DEV__,
  ENABLE_REDUX_DEVTOOLS: __DEV__,
  LOG_LEVEL: __DEV__ ? 'debug' : 'error',
  MOCK_API_RESPONSES: false,
};
