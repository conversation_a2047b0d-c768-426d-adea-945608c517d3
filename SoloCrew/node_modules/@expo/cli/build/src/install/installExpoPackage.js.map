{"version": 3, "sources": ["../../../src/install/installExpoPackage.ts"], "sourcesContent": ["import * as PackageManager from '@expo/package-manager';\nimport spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport type { Options } from './resolveOptions';\nimport { getRunningProcess } from '../utils/getRunningProcess';\n\n/**\n * Given a list of incompatible packages, installs the correct versions of the packages with the package manager used for the project.\n * This exits immediately after spawning the install command, since the command shouldn't remain running while it is being updated.\n */\nexport async function installExpoPackageAsync(\n  projectRoot: string,\n  {\n    packageManager,\n    packageManagerArguments,\n    expoPackageToInstall,\n    followUpCommandArgs,\n    dev,\n  }: Pick<Options, 'dev'> & {\n    /** Package manager to use when installing the versioned packages. */\n    packageManager: PackageManager.NodePackageManager;\n    /**\n     * Extra parameters to pass to the `packageManager` when installing versioned packages.\n     * @example ['--no-save']\n     */\n    packageManagerArguments: string[];\n    expoPackageToInstall: string;\n    followUpCommandArgs: string[];\n  }\n) {\n  // Check if there's potentially a dev server running in the current folder and warn about it\n  // (not guaranteed to be Expo CLI, and the CLI isn't always running on 8081, but it's a good guess)\n  const isExpoMaybeRunningForProject = !!(await getRunningProcess(8081));\n\n  if (isExpoMaybeRunningForProject) {\n    Log.warn(\n      'The Expo CLI appears to be running this project in another terminal window. Close and restart any Expo CLI instances after the installation to complete the update.'\n    );\n  }\n\n  // Safe to use current process to upgrade Expo package- doesn't affect current process\n  try {\n    if (dev) {\n      await packageManager.addDevAsync([...packageManagerArguments, expoPackageToInstall]);\n    } else {\n      await packageManager.addAsync([...packageManagerArguments, expoPackageToInstall]);\n    }\n  } catch (error) {\n    Log.error(\n      chalk`Cannot install the latest Expo package. Install {bold expo@latest} with ${packageManager.name} and then run {bold npx expo install} again.`\n    );\n    throw error;\n  }\n\n  // Spawn a new process to install the rest of the packages if there are any, as only then will the latest Expo package be used\n  if (followUpCommandArgs.length) {\n    let commandSegments = ['expo', 'install', dev ? '--dev' : '', ...followUpCommandArgs].filter(\n      Boolean\n    );\n    if (packageManagerArguments.length) {\n      commandSegments = [...commandSegments, '--', ...packageManagerArguments];\n    }\n\n    Log.log(chalk`\\u203A Running {bold npx expo install} under the updated expo version`);\n    Log.log('> ' + commandSegments.join(' '));\n\n    await spawnAsync('npx', commandSegments, {\n      stdio: 'inherit',\n      cwd: projectRoot,\n      env: { ...process.env },\n    });\n  }\n}\n"], "names": ["installExpoPackageAsync", "projectRoot", "packageManager", "packageManagerArguments", "expoPackageToInstall", "followUpCommandArgs", "dev", "isExpoMaybeRunningForProject", "getRunningProcess", "Log", "warn", "addDevAsync", "addAsync", "error", "chalk", "name", "length", "commandSegments", "filter", "Boolean", "log", "join", "spawnAsync", "stdio", "cwd", "env", "process"], "mappings": ";;;;+BAYsBA;;;eAAAA;;;;gEAXC;;;;;;;gEACL;;;;;;6DAEG;mCAEa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B,eAAeA,wBACpBC,WAAmB,EACnB,EACEC,cAAc,EACdC,uBAAuB,EACvBC,oBAAoB,EACpBC,mBAAmB,EACnBC,GAAG,EAWJ;IAED,4FAA4F;IAC5F,mGAAmG;IACnG,MAAMC,+BAA+B,CAAC,CAAE,MAAMC,IAAAA,oCAAiB,EAAC;IAEhE,IAAID,8BAA8B;QAChCE,KAAIC,IAAI,CACN;IAEJ;IAEA,sFAAsF;IACtF,IAAI;QACF,IAAIJ,KAAK;YACP,MAAMJ,eAAeS,WAAW,CAAC;mBAAIR;gBAAyBC;aAAqB;QACrF,OAAO;YACL,MAAMF,eAAeU,QAAQ,CAAC;mBAAIT;gBAAyBC;aAAqB;QAClF;IACF,EAAE,OAAOS,OAAO;QACdJ,KAAII,KAAK,CACPC,IAAAA,gBAAK,CAAA,CAAC,wEAAwE,EAAEZ,eAAea,IAAI,CAAC,4CAA4C,CAAC;QAEnJ,MAAMF;IACR;IAEA,8HAA8H;IAC9H,IAAIR,oBAAoBW,MAAM,EAAE;QAC9B,IAAIC,kBAAkB;YAAC;YAAQ;YAAWX,MAAM,UAAU;eAAOD;SAAoB,CAACa,MAAM,CAC1FC;QAEF,IAAIhB,wBAAwBa,MAAM,EAAE;YAClCC,kBAAkB;mBAAIA;gBAAiB;mBAASd;aAAwB;QAC1E;QAEAM,KAAIW,GAAG,CAACN,IAAAA,gBAAK,CAAA,CAAC,qEAAqE,CAAC;QACpFL,KAAIW,GAAG,CAAC,OAAOH,gBAAgBI,IAAI,CAAC;QAEpC,MAAMC,IAAAA,qBAAU,EAAC,OAAOL,iBAAiB;YACvCM,OAAO;YACPC,KAAKvB;YACLwB,KAAK;gBAAE,GAAGC,QAAQD,GAAG;YAAC;QACxB;IACF;AACF"}