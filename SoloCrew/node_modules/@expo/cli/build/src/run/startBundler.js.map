{"version": 3, "sources": ["../../../src/run/startBundler.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport * as Log from '../log';\nimport { startInterfaceAsync } from '../start/interface/startInterface';\nimport { BundlerStartOptions } from '../start/server/BundlerDevServer';\nimport { DevServerManager } from '../start/server/DevServerManager';\nimport { env } from '../utils/env';\nimport { isInteractive } from '../utils/interactive';\n\nexport async function startBundlerAsync(\n  projectRoot: string,\n  {\n    port,\n    headless,\n    scheme,\n  }: {\n    port: number;\n    headless?: boolean;\n    scheme?: string;\n  }\n): Promise<DevServerManager> {\n  const options: BundlerStartOptions = {\n    port,\n    headless,\n    devClient: true,\n    minify: false,\n\n    location: {\n      scheme,\n    },\n  };\n\n  const devServerManager = await DevServerManager.startMetroAsync(projectRoot, options);\n\n  // Present the Terminal UI.\n  if (!headless && isInteractive()) {\n    // Only read the config if we are going to use the results.\n    const { exp } = getConfig(projectRoot, {\n      // We don't need very many fields here, just use the lightest possible read.\n      skipSDKVersionRequirement: true,\n      skipPlugins: true,\n    });\n    await startInterfaceAsync(devServerManager, {\n      platforms: exp.platforms ?? [],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n\n    if (url) {\n      if (env.__EXPO_E2E_TEST) {\n        // Print the URL to stdout for tests\n        console.info(`[__EXPO_E2E_TEST:server] ${JSON.stringify({ url })}`);\n      }\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  if (!options.headless) {\n    await devServerManager.watchEnvironmentVariables();\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  return devServerManager;\n}\n"], "names": ["startBundlerAsync", "projectRoot", "port", "headless", "scheme", "options", "devClient", "minify", "location", "devServerManager", "DevServerManager", "startMetroAsync", "isInteractive", "exp", "getConfig", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "startInterfaceAsync", "platforms", "url", "getDefaultDevServer", "getDevServerUrl", "env", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "Log", "log", "chalk", "watchEnvironmentVariables", "bootstrapTypeScriptAsync"], "mappings": ";;;;+BAUsBA;;;eAAAA;;;;yBAVI;;;;;;;gEACR;;;;;;6DAEG;gCACe;kCAEH;qBACb;6BACU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEvB,eAAeA,kBACpBC,WAAmB,EACnB,EACEC,IAAI,EACJC,QAAQ,EACRC,MAAM,EAKP;IAED,MAAMC,UAA+B;QACnCH;QACAC;QACAG,WAAW;QACXC,QAAQ;QAERC,UAAU;YACRJ;QACF;IACF;IAEA,MAAMK,mBAAmB,MAAMC,kCAAgB,CAACC,eAAe,CAACV,aAAaI;IAE7E,2BAA2B;IAC3B,IAAI,CAACF,YAAYS,IAAAA,0BAAa,KAAI;QAChC,2DAA2D;QAC3D,MAAM,EAAEC,GAAG,EAAE,GAAGC,IAAAA,mBAAS,EAACb,aAAa;YACrC,4EAA4E;YAC5Ec,2BAA2B;YAC3BC,aAAa;QACf;QACA,MAAMC,IAAAA,mCAAmB,EAACR,kBAAkB;YAC1CS,WAAWL,IAAIK,SAAS,IAAI,EAAE;QAChC;IACF,OAAO;YAEOT;QADZ,uCAAuC;QACvC,MAAMU,OAAMV,wCAAAA,iBAAiBW,mBAAmB,uBAApCX,sCAAwCY,eAAe;QAEnE,IAAIF,KAAK;YACP,IAAIG,QAAG,CAACC,eAAe,EAAE;gBACvB,oCAAoC;gBACpCC,QAAQC,IAAI,CAAC,CAAC,yBAAyB,EAAEC,KAAKC,SAAS,CAAC;oBAAER;gBAAI,IAAI;YACpE;YACAS,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,sBAAsB,EAAEX,IAAI,CAAC,CAAC;QAC9C;IACF;IAEA,IAAI,CAACd,QAAQF,QAAQ,EAAE;QACrB,MAAMM,iBAAiBsB,yBAAyB;QAChD,MAAMtB,iBAAiBuB,wBAAwB;IACjD;IAEA,OAAOvB;AACT"}