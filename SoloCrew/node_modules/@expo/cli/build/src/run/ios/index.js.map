{"version": 3, "sources": ["../../../../src/run/ios/index.ts"], "sourcesContent": ["#!/usr/bin/env node\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport path from 'path';\n\nimport { XcodeConfiguration } from './XcodeBuild.types';\nimport { Command } from '../../../bin/cli';\nimport { assertWithOptionsArgs, printHelp } from '../../utils/args';\nimport { logCmdError } from '../../utils/errors';\n\nexport const expoRunIos: Command = async (argv) => {\n  const rawArgsMap: arg.Spec = {\n    // Types\n    '--help': Boolean,\n    '--no-build-cache': Boolean,\n    '--no-install': Boolean,\n    '--no-bundler': Boolean,\n    '--configuration': String,\n    '--binary': String,\n\n    '--port': Number,\n\n    // Undocumented flag for re-bundling the app and assets for a build to try different JS code in release builds.\n    // Also updates the app.json.\n    '--unstable-rebundle': <PERSON><PERSON><PERSON>,\n    // Aliases\n    '-p': '--port',\n\n    '-h': '--help',\n  };\n  const args = assertWithOptionsArgs(rawArgsMap, {\n    argv,\n\n    permissive: true,\n  });\n\n  // '-d' -> '--device': Boolean,\n  // '--scheme': String,\n\n  if (args['--help']) {\n    printHelp(\n      `Run the iOS app binary locally`,\n      `npx expo run:ios`,\n      [\n        `--no-build-cache                 Clear the native derived data before building`,\n        `--no-install                     Skip installing dependencies`,\n        `--no-bundler                     Skip starting the Metro bundler`,\n        `--scheme [scheme]                Scheme to build`,\n        `--binary <path>                  Path to existing .app or .ipa to install.`,\n        chalk`--configuration <configuration>  Xcode configuration to use. Debug or Release. {dim Default: Debug}`,\n        `-d, --device [device]            Device name or UDID to build the app on`,\n        chalk`-p, --port <port>                Port to start the Metro bundler on. {dim Default: 8081}`,\n        `-h, --help                       Usage info`,\n      ].join('\\n'),\n      [\n        '',\n        chalk`  Build for production (unsigned) with the {bold Release} configuration:`,\n        chalk`    {dim $} npx expo run:ios --configuration Release`,\n        '',\n      ].join('\\n')\n    );\n  }\n\n  const { resolveStringOrBooleanArgsAsync } = await import('../../utils/resolveArgs.js');\n  const parsed = await resolveStringOrBooleanArgsAsync(argv ?? [], rawArgsMap, {\n    '--scheme': Boolean,\n    '--device': Boolean,\n    '-d': '--device',\n  }).catch(logCmdError);\n\n  const { runIosAsync } = await import('./runIosAsync.js');\n  return runIosAsync(path.resolve(parsed.projectRoot), {\n    // Parsed options\n    buildCache: !args['--no-build-cache'],\n    install: !args['--no-install'],\n    bundler: !args['--no-bundler'],\n    port: args['--port'],\n    binary: args['--binary'],\n    rebundle: args['--unstable-rebundle'],\n\n    // Custom parsed args\n    device: parsed.args['--device'],\n    scheme: parsed.args['--scheme'],\n    configuration: parsed.args['--configuration'] as XcodeConfiguration,\n  }).catch(logCmdError);\n};\n"], "names": ["expoRunIos", "argv", "rawArgsMap", "Boolean", "String", "Number", "args", "assertWithOptionsArgs", "permissive", "printHelp", "chalk", "join", "resolveStringOrBooleanArgsAsync", "parsed", "catch", "logCmdError", "runIosAsync", "path", "resolve", "projectRoot", "buildCache", "install", "bundler", "port", "binary", "rebundle", "device", "scheme", "configuration"], "mappings": ";;;;;+BAUaA;;;eAAAA;;;;gEARK;;;;;;;gEACD;;;;;;sBAIgC;wBACrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMA,aAAsB,OAAOC;IACxC,MAAMC,aAAuB;QAC3B,QAAQ;QACR,UAAUC;QACV,oBAAoBA;QACpB,gBAAgBA;QAChB,gBAAgBA;QAChB,mBAAmBC;QACnB,YAAYA;QAEZ,UAAUC;QAEV,+GAA+G;QAC/G,6BAA6B;QAC7B,uBAAuBF;QACvB,UAAU;QACV,MAAM;QAEN,MAAM;IACR;IACA,MAAMG,OAAOC,IAAAA,2BAAqB,EAACL,YAAY;QAC7CD;QAEAO,YAAY;IACd;IAEA,+BAA+B;IAC/B,sBAAsB;IAEtB,IAAIF,IAAI,CAAC,SAAS,EAAE;QAClBG,IAAAA,eAAS,EACP,CAAC,8BAA8B,CAAC,EAChC,CAAC,gBAAgB,CAAC,EAClB;YACE,CAAC,8EAA8E,CAAC;YAChF,CAAC,6DAA6D,CAAC;YAC/D,CAAC,gEAAgE,CAAC;YAClE,CAAC,gDAAgD,CAAC;YAClD,CAAC,0EAA0E,CAAC;YAC5EC,IAAAA,gBAAK,CAAA,CAAC,mGAAmG,CAAC;YAC1G,CAAC,wEAAwE,CAAC;YAC1EA,IAAAA,gBAAK,CAAA,CAAC,wFAAwF,CAAC;YAC/F,CAAC,2CAA2C,CAAC;SAC9C,CAACC,IAAI,CAAC,OACP;YACE;YACAD,IAAAA,gBAAK,CAAA,CAAC,wEAAwE,CAAC;YAC/EA,IAAAA,gBAAK,CAAA,CAAC,oDAAoD,CAAC;YAC3D;SACD,CAACC,IAAI,CAAC;IAEX;IAEA,MAAM,EAAEC,+BAA+B,EAAE,GAAG,MAAM,mEAAA,QAAO;IACzD,MAAMC,SAAS,MAAMD,gCAAgCX,QAAQ,EAAE,EAAEC,YAAY;QAC3E,YAAYC;QACZ,YAAYA;QACZ,MAAM;IACR,GAAGW,KAAK,CAACC,mBAAW;IAEpB,MAAM,EAAEC,WAAW,EAAE,GAAG,MAAM,mEAAA,QAAO;IACrC,OAAOA,YAAYC,eAAI,CAACC,OAAO,CAACL,OAAOM,WAAW,GAAG;QACnD,iBAAiB;QACjBC,YAAY,CAACd,IAAI,CAAC,mBAAmB;QACrCe,SAAS,CAACf,IAAI,CAAC,eAAe;QAC9BgB,SAAS,CAAChB,IAAI,CAAC,eAAe;QAC9BiB,MAAMjB,IAAI,CAAC,SAAS;QACpBkB,QAAQlB,IAAI,CAAC,WAAW;QACxBmB,UAAUnB,IAAI,CAAC,sBAAsB;QAErC,qBAAqB;QACrBoB,QAAQb,OAAOP,IAAI,CAAC,WAAW;QAC/BqB,QAAQd,OAAOP,IAAI,CAAC,WAAW;QAC/BsB,eAAef,OAAOP,IAAI,CAAC,kBAAkB;IAC/C,GAAGQ,KAAK,CAACC,mBAAW;AACtB"}