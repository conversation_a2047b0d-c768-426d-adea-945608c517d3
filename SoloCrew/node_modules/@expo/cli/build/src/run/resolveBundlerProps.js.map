{"version": 3, "sources": ["../../../src/run/resolveBundlerProps.ts"], "sourcesContent": ["import { Log } from '../log';\nimport { CommandError } from '../utils/errors';\nimport { resolvePortAsync } from '../utils/port';\n\nexport interface BundlerProps {\n  /** Port to start the dev server on. */\n  port: number;\n  /** Skip opening the bundler from the native script. */\n  shouldStartBundler: boolean;\n}\n\nexport async function resolveBundlerPropsAsync(\n  projectRoot: string,\n  options: {\n    port?: number;\n    bundler?: boolean;\n  }\n): Promise<BundlerProps> {\n  options.bundler = options.bundler ?? true;\n\n  if (\n    // If the user disables the bundler then they should not pass in the port property.\n    !options.bundler &&\n    options.port\n  ) {\n    throw new CommandError('BAD_ARGS', '--port and --no-bundler are mutually exclusive arguments');\n  }\n\n  // Resolve the port if the bundler is used.\n  let port = options.bundler\n    ? await resolvePortAsync(projectRoot, { reuseExistingPort: true, defaultPort: options.port })\n    : null;\n\n  // Skip bundling if the port is null -- meaning skip the bundler if the port is already running the app.\n  options.bundler = !!port;\n  if (!port) {\n    // Use explicit user-provided port, or the default port\n    port = options.port ?? 8081;\n  }\n  Log.debug(`Resolved port: ${port}, start dev server: ${options.bundler}`);\n\n  return {\n    shouldStartBundler: !!options.bundler,\n    port,\n  };\n}\n"], "names": ["resolveBundlerPropsAsync", "projectRoot", "options", "bundler", "port", "CommandError", "resolvePortAsync", "reuseExistingPort", "defaultPort", "Log", "debug", "shouldStartBundler"], "mappings": ";;;;+BAWsBA;;;eAAAA;;;qBAXF;wBACS;sBACI;AAS1B,eAAeA,yBACpBC,WAAmB,EACnBC,OAGC;IAEDA,QAAQC,OAAO,GAAGD,QAAQC,OAAO,IAAI;IAErC,IACE,mFAAmF;IACnF,CAACD,QAAQC,OAAO,IAChBD,QAAQE,IAAI,EACZ;QACA,MAAM,IAAIC,oBAAY,CAAC,YAAY;IACrC;IAEA,2CAA2C;IAC3C,IAAID,OAAOF,QAAQC,OAAO,GACtB,MAAMG,IAAAA,sBAAgB,EAACL,aAAa;QAAEM,mBAAmB;QAAMC,aAAaN,QAAQE,IAAI;IAAC,KACzF;IAEJ,wGAAwG;IACxGF,QAAQC,OAAO,GAAG,CAAC,CAACC;IACpB,IAAI,CAACA,MAAM;QACT,uDAAuD;QACvDA,OAAOF,QAAQE,IAAI,IAAI;IACzB;IACAK,QAAG,CAACC,KAAK,CAAC,CAAC,eAAe,EAAEN,KAAK,oBAAoB,EAAEF,QAAQC,OAAO,EAAE;IAExE,OAAO;QACLQ,oBAAoB,CAAC,CAACT,QAAQC,OAAO;QACrCC;IACF;AACF"}