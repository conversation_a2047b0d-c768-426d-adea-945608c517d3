{"version": 3, "sources": ["../../../src/run/ensureNativeProject.ts"], "sourcesContent": ["import { ModPlatform } from '@expo/config-plugins';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { promptToClearMalformedNativeProjectsAsync } from '../prebuild/clearNativeFolder';\nimport { prebuildAsync } from '../prebuild/prebuildAsync';\nimport { profile } from '../utils/profile';\n\nexport async function ensureNativeProjectAsync(\n  projectRoot: string,\n  { platform, install }: { platform: ModPlatform; install?: boolean }\n) {\n  // If the user has an empty android folder then the project won't build, this can happen when they delete the prebuild files in git.\n  // Check to ensure most of the core files are in place, and prompt to remove the folder if they aren't.\n  await profile(promptToClearMalformedNativeProjectsAsync)(projectRoot, [platform]);\n\n  // If the project doesn't have native code, prebuild it...\n  if (!fs.existsSync(path.join(projectRoot, platform))) {\n    await prebuildAsync(projectRoot, {\n      install: !!install,\n      platforms: [platform],\n    });\n  } else {\n    return true;\n  }\n  return false;\n}\n"], "names": ["ensureNativeProjectAsync", "projectRoot", "platform", "install", "profile", "promptToClearMalformedNativeProjectsAsync", "fs", "existsSync", "path", "join", "prebuildAsync", "platforms"], "mappings": ";;;;+BAQsBA;;;eAAAA;;;;gEAPP;;;;;;;gEACE;;;;;;mCAEyC;+BAC5B;yBACN;;;;;;AAEjB,eAAeA,yBACpBC,WAAmB,EACnB,EAAEC,QAAQ,EAAEC,OAAO,EAAgD;IAEnE,oIAAoI;IACpI,uGAAuG;IACvG,MAAMC,IAAAA,gBAAO,EAACC,4DAAyC,EAAEJ,aAAa;QAACC;KAAS;IAEhF,0DAA0D;IAC1D,IAAI,CAACI,aAAE,CAACC,UAAU,CAACC,eAAI,CAACC,IAAI,CAACR,aAAaC,YAAY;QACpD,MAAMQ,IAAAA,4BAAa,EAACT,aAAa;YAC/BE,SAAS,CAAC,CAACA;YACXQ,WAAW;gBAACT;aAAS;QACvB;IACF,OAAO;QACL,OAAO;IACT;IACA,OAAO;AACT"}