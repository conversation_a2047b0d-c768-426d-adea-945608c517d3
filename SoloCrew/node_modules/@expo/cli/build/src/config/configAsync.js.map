{"version": 3, "sources": ["../../../src/config/configAsync.ts"], "sourcesContent": ["import { ExpoConfig, getConfig, ProjectConfig } from '@expo/config';\nimport assert from 'assert';\nimport util from 'util';\n\nimport * as Log from '../log';\nimport { CommandError } from '../utils/errors';\nimport { setNodeEnv } from '../utils/nodeEnv';\nimport { profile } from '../utils/profile';\n\ntype Options = {\n  type?: string;\n  full?: boolean;\n  json?: boolean;\n};\n\nexport function logConfig(config: ExpoConfig | ProjectConfig) {\n  const isObjStr = (str: string): boolean => /^\\w+: {/g.test(str);\n  Log.log(\n    util.inspect(config, {\n      colors: true,\n      compact: false,\n      // Sort objects to the end so that smaller values aren't hidden between large objects.\n      sorted(a: string, b: string) {\n        if (isObjStr(a)) return 1;\n        if (isObjStr(b)) return -1;\n        return 0;\n      },\n      showHidden: false,\n      depth: null,\n    })\n  );\n}\n\nexport async function configAsync(projectRoot: string, options: Options) {\n  const loggingFunctions = {\n    log: console.log,\n    warn: console.warn,\n    error: console.error,\n  };\n  // Disable logging for this command if the user wants to get JSON output.\n  // This will ensure that only the JSON is printed to stdout.\n  if (options.json) {\n    console.log = function () {};\n    console.warn = function () {};\n    console.error = function () {};\n  }\n  setNodeEnv('development');\n  require('@expo/env').load(projectRoot);\n\n  if (options.type) {\n    assert.match(options.type, /^(public|prebuild|introspect)$/);\n  }\n\n  let config: ProjectConfig;\n\n  if (options.type === 'prebuild') {\n    const { getPrebuildConfigAsync } = await import('@expo/prebuild-config');\n\n    config = await profile(getPrebuildConfigAsync)(projectRoot, {\n      platforms: ['ios', 'android'],\n    });\n  } else if (options.type === 'introspect') {\n    const { getPrebuildConfigAsync } = await import('@expo/prebuild-config');\n    const { compileModsAsync } = await import('@expo/config-plugins/build/plugins/mod-compiler.js');\n\n    config = await profile(getPrebuildConfigAsync)(projectRoot, {\n      platforms: ['ios', 'android'],\n    });\n\n    await compileModsAsync(config.exp, {\n      projectRoot,\n      introspect: true,\n      platforms: ['ios', 'android'],\n      assertMissingModProviders: false,\n    });\n    // @ts-ignore\n    delete config.modRequest;\n    // @ts-ignore\n    delete config.modResults;\n  } else if (options.type === 'public') {\n    config = profile(getConfig)(projectRoot, {\n      skipSDKVersionRequirement: true,\n      isPublicConfig: true,\n    });\n  } else if (options.type) {\n    throw new CommandError(\n      `Invalid option: --type ${options.type}. Valid options are: public, prebuild`\n    );\n  } else {\n    config = profile(getConfig)(projectRoot, {\n      skipSDKVersionRequirement: true,\n    });\n  }\n\n  const configOutput = options.full ? config : config.exp;\n\n  if (!options.json) {\n    Log.log();\n    logConfig(configOutput);\n    Log.log();\n  } else {\n    process.stdout.write(JSON.stringify(configOutput));\n\n    // Re-enable logging functions for testing.\n    console.log = loggingFunctions.log;\n    console.warn = loggingFunctions.warn;\n    console.error = loggingFunctions.error;\n  }\n}\n"], "names": ["config<PERSON><PERSON>", "logConfig", "config", "isObjStr", "str", "test", "Log", "log", "util", "inspect", "colors", "compact", "sorted", "a", "b", "showHidden", "depth", "projectRoot", "options", "loggingFunctions", "console", "warn", "error", "json", "setNodeEnv", "require", "load", "type", "assert", "match", "getPrebuildConfigAsync", "profile", "platforms", "compileModsAsync", "exp", "introspect", "assertMissingModProviders", "modRequest", "modResults", "getConfig", "skipSDKVersionRequirement", "isPublicConfig", "CommandError", "configOutput", "full", "process", "stdout", "write", "JSON", "stringify"], "mappings": ";;;;;;;;;;;IAiCsBA,WAAW;eAAXA;;IAlBNC,SAAS;eAATA;;;;yBAfqC;;;;;;;gEAClC;;;;;;;gEACF;;;;;;6DAEI;wBACQ;yBACF;yBACH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjB,SAASA,UAAUC,MAAkC;IAC1D,MAAMC,WAAW,CAACC,MAAyB,WAAWC,IAAI,CAACD;IAC3DE,KAAIC,GAAG,CACLC,eAAI,CAACC,OAAO,CAACP,QAAQ;QACnBQ,QAAQ;QACRC,SAAS;QACT,sFAAsF;QACtFC,QAAOC,CAAS,EAAEC,CAAS;YACzB,IAAIX,SAASU,IAAI,OAAO;YACxB,IAAIV,SAASW,IAAI,OAAO,CAAC;YACzB,OAAO;QACT;QACAC,YAAY;QACZC,OAAO;IACT;AAEJ;AAEO,eAAehB,YAAYiB,WAAmB,EAAEC,OAAgB;IACrE,MAAMC,mBAAmB;QACvBZ,KAAKa,QAAQb,GAAG;QAChBc,MAAMD,QAAQC,IAAI;QAClBC,OAAOF,QAAQE,KAAK;IACtB;IACA,yEAAyE;IACzE,4DAA4D;IAC5D,IAAIJ,QAAQK,IAAI,EAAE;QAChBH,QAAQb,GAAG,GAAG,YAAa;QAC3Ba,QAAQC,IAAI,GAAG,YAAa;QAC5BD,QAAQE,KAAK,GAAG,YAAa;IAC/B;IACAE,IAAAA,mBAAU,EAAC;IACXC,QAAQ,aAAaC,IAAI,CAACT;IAE1B,IAAIC,QAAQS,IAAI,EAAE;QAChBC,iBAAM,CAACC,KAAK,CAACX,QAAQS,IAAI,EAAE;IAC7B;IAEA,IAAIzB;IAEJ,IAAIgB,QAAQS,IAAI,KAAK,YAAY;QAC/B,MAAM,EAAEG,sBAAsB,EAAE,GAAG,MAAM,mEAAA,QAAO;QAEhD5B,SAAS,MAAM6B,IAAAA,gBAAO,EAACD,wBAAwBb,aAAa;YAC1De,WAAW;gBAAC;gBAAO;aAAU;QAC/B;IACF,OAAO,IAAId,QAAQS,IAAI,KAAK,cAAc;QACxC,MAAM,EAAEG,sBAAsB,EAAE,GAAG,MAAM,mEAAA,QAAO;QAChD,MAAM,EAAEG,gBAAgB,EAAE,GAAG,MAAM,mEAAA,QAAO;QAE1C/B,SAAS,MAAM6B,IAAAA,gBAAO,EAACD,wBAAwBb,aAAa;YAC1De,WAAW;gBAAC;gBAAO;aAAU;QAC/B;QAEA,MAAMC,iBAAiB/B,OAAOgC,GAAG,EAAE;YACjCjB;YACAkB,YAAY;YACZH,WAAW;gBAAC;gBAAO;aAAU;YAC7BI,2BAA2B;QAC7B;QACA,aAAa;QACb,OAAOlC,OAAOmC,UAAU;QACxB,aAAa;QACb,OAAOnC,OAAOoC,UAAU;IAC1B,OAAO,IAAIpB,QAAQS,IAAI,KAAK,UAAU;QACpCzB,SAAS6B,IAAAA,gBAAO,EAACQ,mBAAS,EAAEtB,aAAa;YACvCuB,2BAA2B;YAC3BC,gBAAgB;QAClB;IACF,OAAO,IAAIvB,QAAQS,IAAI,EAAE;QACvB,MAAM,IAAIe,oBAAY,CACpB,CAAC,uBAAuB,EAAExB,QAAQS,IAAI,CAAC,qCAAqC,CAAC;IAEjF,OAAO;QACLzB,SAAS6B,IAAAA,gBAAO,EAACQ,mBAAS,EAAEtB,aAAa;YACvCuB,2BAA2B;QAC7B;IACF;IAEA,MAAMG,eAAezB,QAAQ0B,IAAI,GAAG1C,SAASA,OAAOgC,GAAG;IAEvD,IAAI,CAAChB,QAAQK,IAAI,EAAE;QACjBjB,KAAIC,GAAG;QACPN,UAAU0C;QACVrC,KAAIC,GAAG;IACT,OAAO;QACLsC,QAAQC,MAAM,CAACC,KAAK,CAACC,KAAKC,SAAS,CAACN;QAEpC,2CAA2C;QAC3CvB,QAAQb,GAAG,GAAGY,iBAAiBZ,GAAG;QAClCa,QAAQC,IAAI,GAAGF,iBAAiBE,IAAI;QACpCD,QAAQE,KAAK,GAAGH,iBAAiBG,KAAK;IACxC;AACF"}