{"version": 3, "sources": ["../../../src/utils/downloadExpoGoAsync.ts"], "sourcesContent": ["import path from 'path';\nimport ProgressBar from 'progress';\nimport { gt } from 'semver';\n\nimport { downloadAppAsync } from './downloadAppAsync';\nimport { CommandError } from './errors';\nimport { ora } from './ora';\nimport { profile } from './profile';\nimport { createProgressBar } from './progress';\nimport { getVersionsAsync, SDKVersion } from '../api/getVersions';\nimport { getExpoHomeDirectory } from '../api/user/UserSettings';\nimport { Log } from '../log';\n\nconst debug = require('debug')('expo:utils:downloadExpoGo') as typeof console.log;\n\nconst platformSettings: Record<\n  string,\n  {\n    shouldExtractResults: boolean;\n    versionsKey: keyof SDKVersion;\n    getFilePath: (filename: string) => string;\n  }\n> = {\n  ios: {\n    versionsKey: 'iosClientUrl',\n    getFilePath: (filename) =>\n      path.join(getExpoHomeDirectory(), 'ios-simulator-app-cache', `${filename}.app`),\n    shouldExtractResults: true,\n  },\n  android: {\n    versionsKey: 'androidClientUrl',\n    getFilePath: (filename) =>\n      path.join(getExpoHomeDirectory(), 'android-apk-cache', `${filename}.apk`),\n    shouldExtractResults: false,\n  },\n};\n\n/**\n * @internal exposed for testing.\n * @returns the matching `SDKVersion` object from the Expo API.\n */\nexport async function getExpoGoVersionEntryAsync(sdkVersion: string): Promise<SDKVersion> {\n  const { sdkVersions: versions } = await getVersionsAsync();\n  let version: SDKVersion;\n\n  if (sdkVersion.toUpperCase() === 'UNVERSIONED') {\n    // find the latest version\n    const latestVersionKey = Object.keys(versions).reduce((a, b) => {\n      if (gt(b, a)) {\n        return b;\n      }\n      return a;\n    }, '0.0.0');\n\n    Log.warn(\n      `Downloading the latest Expo Go client (${latestVersionKey}). This will not fully conform to UNVERSIONED.`\n    );\n    version = versions[latestVersionKey];\n  } else {\n    version = versions[sdkVersion];\n  }\n\n  if (!version) {\n    throw new CommandError(`Unable to find a version of Expo Go for SDK ${sdkVersion}`);\n  }\n  return version;\n}\n\n/** Download the Expo Go app from the Expo servers (if only it was this easy for every app). */\nexport async function downloadExpoGoAsync(\n  platform: keyof typeof platformSettings,\n  {\n    url,\n    sdkVersion,\n  }: {\n    url?: string;\n    sdkVersion: string;\n  }\n): Promise<string> {\n  const { getFilePath, versionsKey, shouldExtractResults } = platformSettings[platform];\n\n  const spinner = ora({ text: 'Fetching Expo Go', color: 'white' }).start();\n\n  let bar: ProgressBar | null = null;\n\n  try {\n    if (!url) {\n      const version = await getExpoGoVersionEntryAsync(sdkVersion);\n\n      debug(`Installing Expo Go version for SDK ${sdkVersion} at URL: ${version[versionsKey]}`);\n      url = version[versionsKey] as string;\n    }\n  } catch (error) {\n    spinner.fail();\n    throw error;\n  }\n\n  const filename = path.parse(url).name;\n\n  try {\n    const outputPath = getFilePath(filename);\n    debug(`Downloading Expo Go from \"${url}\" to \"${outputPath}\".`);\n    debug(\n      `The requested copy of Expo Go might already be cached in: \"${getExpoHomeDirectory()}\". You can disable the cache with EXPO_NO_CACHE=1`\n    );\n    await profile(downloadAppAsync)({\n      url,\n      // Save all encrypted cache data to `~/.expo/expo-go`\n      cacheDirectory: 'expo-go',\n      outputPath,\n      extract: shouldExtractResults,\n      onProgress({ progress, total }) {\n        if (progress && total) {\n          if (!bar) {\n            if (spinner.isSpinning) {\n              spinner.stop();\n            }\n            bar = createProgressBar('Downloading the Expo Go app [:bar] :percent :etas', {\n              width: 64,\n              total: 100,\n              // clear: true,\n              complete: '=',\n              incomplete: ' ',\n            });\n          } else {\n            bar!.update(progress, total);\n          }\n        }\n      },\n    });\n    return outputPath;\n  } finally {\n    spinner.stop();\n    // @ts-expect-error\n    bar?.terminate();\n  }\n}\n"], "names": ["downloadExpoGoAsync", "getExpoGoVersionEntryAsync", "debug", "require", "platformSettings", "ios", "versionsKey", "getFilePath", "filename", "path", "join", "getExpoHomeDirectory", "shouldExtractResults", "android", "sdkVersion", "sdkVersions", "versions", "getVersionsAsync", "version", "toUpperCase", "latestVersionKey", "Object", "keys", "reduce", "a", "b", "gt", "Log", "warn", "CommandError", "platform", "url", "spinner", "ora", "text", "color", "start", "bar", "error", "fail", "parse", "name", "outputPath", "profile", "downloadAppAsync", "cacheDirectory", "extract", "onProgress", "progress", "total", "isSpinning", "stop", "createProgressBar", "width", "complete", "incomplete", "update", "terminate"], "mappings": ";;;;;;;;;;;IAqEsBA,mBAAmB;eAAnBA;;IA5BAC,0BAA0B;eAA1BA;;;;gEAzCL;;;;;;;yBAEE;;;;;;kCAEc;wBACJ;qBACT;yBACI;0BACU;6BACW;8BACR;qBACjB;;;;;;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,mBAOF;IACFC,KAAK;QACHC,aAAa;QACbC,aAAa,CAACC,WACZC,eAAI,CAACC,IAAI,CAACC,IAAAA,kCAAoB,KAAI,2BAA2B,GAAGH,SAAS,IAAI,CAAC;QAChFI,sBAAsB;IACxB;IACAC,SAAS;QACPP,aAAa;QACbC,aAAa,CAACC,WACZC,eAAI,CAACC,IAAI,CAACC,IAAAA,kCAAoB,KAAI,qBAAqB,GAAGH,SAAS,IAAI,CAAC;QAC1EI,sBAAsB;IACxB;AACF;AAMO,eAAeX,2BAA2Ba,UAAkB;IACjE,MAAM,EAAEC,aAAaC,QAAQ,EAAE,GAAG,MAAMC,IAAAA,6BAAgB;IACxD,IAAIC;IAEJ,IAAIJ,WAAWK,WAAW,OAAO,eAAe;QAC9C,0BAA0B;QAC1B,MAAMC,mBAAmBC,OAAOC,IAAI,CAACN,UAAUO,MAAM,CAAC,CAACC,GAAGC;YACxD,IAAIC,IAAAA,YAAE,EAACD,GAAGD,IAAI;gBACZ,OAAOC;YACT;YACA,OAAOD;QACT,GAAG;QAEHG,QAAG,CAACC,IAAI,CACN,CAAC,uCAAuC,EAAER,iBAAiB,8CAA8C,CAAC;QAE5GF,UAAUF,QAAQ,CAACI,iBAAiB;IACtC,OAAO;QACLF,UAAUF,QAAQ,CAACF,WAAW;IAChC;IAEA,IAAI,CAACI,SAAS;QACZ,MAAM,IAAIW,oBAAY,CAAC,CAAC,4CAA4C,EAAEf,YAAY;IACpF;IACA,OAAOI;AACT;AAGO,eAAelB,oBACpB8B,QAAuC,EACvC,EACEC,GAAG,EACHjB,UAAU,EAIX;IAED,MAAM,EAAEP,WAAW,EAAED,WAAW,EAAEM,oBAAoB,EAAE,GAAGR,gBAAgB,CAAC0B,SAAS;IAErF,MAAME,UAAUC,IAAAA,QAAG,EAAC;QAAEC,MAAM;QAAoBC,OAAO;IAAQ,GAAGC,KAAK;IAEvE,IAAIC,MAA0B;IAE9B,IAAI;QACF,IAAI,CAACN,KAAK;YACR,MAAMb,UAAU,MAAMjB,2BAA2Ba;YAEjDZ,MAAM,CAAC,mCAAmC,EAAEY,WAAW,SAAS,EAAEI,OAAO,CAACZ,YAAY,EAAE;YACxFyB,MAAMb,OAAO,CAACZ,YAAY;QAC5B;IACF,EAAE,OAAOgC,OAAO;QACdN,QAAQO,IAAI;QACZ,MAAMD;IACR;IAEA,MAAM9B,WAAWC,eAAI,CAAC+B,KAAK,CAACT,KAAKU,IAAI;IAErC,IAAI;QACF,MAAMC,aAAanC,YAAYC;QAC/BN,MAAM,CAAC,0BAA0B,EAAE6B,IAAI,MAAM,EAAEW,WAAW,EAAE,CAAC;QAC7DxC,MACE,CAAC,2DAA2D,EAAES,IAAAA,kCAAoB,IAAG,iDAAiD,CAAC;QAEzI,MAAMgC,IAAAA,gBAAO,EAACC,kCAAgB,EAAE;YAC9Bb;YACA,qDAAqD;YACrDc,gBAAgB;YAChBH;YACAI,SAASlC;YACTmC,YAAW,EAAEC,QAAQ,EAAEC,KAAK,EAAE;gBAC5B,IAAID,YAAYC,OAAO;oBACrB,IAAI,CAACZ,KAAK;wBACR,IAAIL,QAAQkB,UAAU,EAAE;4BACtBlB,QAAQmB,IAAI;wBACd;wBACAd,MAAMe,IAAAA,2BAAiB,EAAC,qDAAqD;4BAC3EC,OAAO;4BACPJ,OAAO;4BACP,eAAe;4BACfK,UAAU;4BACVC,YAAY;wBACd;oBACF,OAAO;wBACLlB,IAAKmB,MAAM,CAACR,UAAUC;oBACxB;gBACF;YACF;QACF;QACA,OAAOP;IACT,SAAU;QACRV,QAAQmB,IAAI;QACZ,mBAAmB;QACnBd,uBAAAA,IAAKoB,SAAS;IAChB;AACF"}