{"version": 3, "sources": ["../../../src/utils/ip.ts"], "sourcesContent": ["import { lanNetworkSync } from 'lan-network';\n\nexport function getIpAddress(): string {\n  try {\n    const lan = lanNetworkSync();\n    return lan.address;\n  } catch {\n    return '127.0.0.1';\n  }\n}\n"], "names": ["getIpAddress", "lan", "lanNetworkSync", "address"], "mappings": ";;;;+BAEgBA;;;eAAAA;;;;yBAFe;;;;;;AAExB,SAASA;IACd,IAAI;QACF,MAAMC,MAAMC,IAAAA,4BAAc;QAC1B,OAAOD,IAAIE,OAAO;IACpB,EAAE,OAAM;QACN,OAAO;IACT;AACF"}