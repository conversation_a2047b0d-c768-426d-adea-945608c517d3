{"version": 3, "sources": ["../../../src/utils/findUp.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nimport { CommandError } from '../utils/errors';\n\n/** Look up directories until one with a `package.json` can be found, assert if none can be found. */\nexport function findUpProjectRootOrAssert(cwd: string): string {\n  const projectRoot = findUpProjectRoot(cwd);\n  if (!projectRoot) {\n    throw new CommandError(`Project root directory not found (working directory: ${cwd})`);\n  }\n  return projectRoot;\n}\n\nfunction findUpProjectRoot(cwd: string): string | null {\n  const found = resolveFrom.silent(cwd, './package.json');\n  if (found) return path.dirname(found);\n\n  const parent = path.dirname(cwd);\n  if (parent === cwd) return null;\n\n  return findUpProjectRoot(parent);\n}\n\n/**\n * Find a file in the (closest) parent directories.\n * This will recursively look for the file, until the root directory is reached.\n */\nexport function findFileInParents(cwd: string, fileName: string): string | null {\n  const found = resolveFrom.silent(cwd, `./${fileName}`);\n  if (found) return found;\n\n  const parent = path.dirname(cwd);\n  if (parent === cwd) return null;\n\n  return findFileInParents(parent, fileName);\n}\n"], "names": ["findFileInParents", "findUpProjectRootOrAssert", "cwd", "projectRoot", "findUpProjectRoot", "CommandError", "found", "resolveFrom", "silent", "path", "dirname", "parent", "fileName"], "mappings": ";;;;;;;;;;;IA4BgBA,iBAAiB;eAAjBA;;IAtBAC,yBAAyB;eAAzBA;;;;gEANC;;;;;;;gEACO;;;;;;wBAEK;;;;;;AAGtB,SAASA,0BAA0BC,GAAW;IACnD,MAAMC,cAAcC,kBAAkBF;IACtC,IAAI,CAACC,aAAa;QAChB,MAAM,IAAIE,oBAAY,CAAC,CAAC,qDAAqD,EAAEH,IAAI,CAAC,CAAC;IACvF;IACA,OAAOC;AACT;AAEA,SAASC,kBAAkBF,GAAW;IACpC,MAAMI,QAAQC,sBAAW,CAACC,MAAM,CAACN,KAAK;IACtC,IAAII,OAAO,OAAOG,eAAI,CAACC,OAAO,CAACJ;IAE/B,MAAMK,SAASF,eAAI,CAACC,OAAO,CAACR;IAC5B,IAAIS,WAAWT,KAAK,OAAO;IAE3B,OAAOE,kBAAkBO;AAC3B;AAMO,SAASX,kBAAkBE,GAAW,EAAEU,QAAgB;IAC7D,MAAMN,QAAQC,sBAAW,CAACC,MAAM,CAACN,KAAK,CAAC,EAAE,EAAEU,UAAU;IACrD,IAAIN,OAAO,OAAOA;IAElB,MAAMK,SAASF,eAAI,CAACC,OAAO,CAACR;IAC5B,IAAIS,WAAWT,KAAK,OAAO;IAE3B,OAAOF,kBAAkBW,QAAQC;AACnC"}