{"version": 3, "sources": ["../../../src/utils/modifyConfigPlugins.ts"], "sourcesContent": ["import { modifyConfigAsync } from '@expo/config';\n\nimport { warnAboutConfigAndThrow } from './modifyConfigAsync';\nimport * as Log from '../log';\n\nexport async function attemptAddingPluginsAsync(\n  projectRoot: string,\n  plugins: string[]\n): Promise<void> {\n  if (!plugins.length) return;\n\n  const modification = await modifyConfigAsync(\n    projectRoot,\n    { plugins },\n    {\n      skipSDKVersionRequirement: true,\n      skipPlugins: true,\n    }\n  );\n  if (modification.type === 'success') {\n    Log.log(`\\u203A Added config plugin${plugins.length === 1 ? '' : 's'}: ${plugins.join(', ')}`);\n  } else {\n    const exactEdits = {\n      plugins,\n    };\n    warnAboutConfigAndThrow(modification.type, modification.message!, exactEdits);\n  }\n}\n"], "names": ["attemptAddingPluginsAsync", "projectRoot", "plugins", "length", "modification", "modifyConfigAsync", "skipSDKVersionRequirement", "skip<PERSON>lug<PERSON>", "type", "Log", "log", "join", "exactEdits", "warnAboutConfigAndThrow", "message"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;;yBALY;;;;;;mCAEM;6DACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEd,eAAeA,0BACpBC,WAAmB,EACnBC,OAAiB;IAEjB,IAAI,CAACA,QAAQC,MAAM,EAAE;IAErB,MAAMC,eAAe,MAAMC,IAAAA,2BAAiB,EAC1CJ,aACA;QAAEC;IAAQ,GACV;QACEI,2BAA2B;QAC3BC,aAAa;IACf;IAEF,IAAIH,aAAaI,IAAI,KAAK,WAAW;QACnCC,KAAIC,GAAG,CAAC,CAAC,0BAA0B,EAAER,QAAQC,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,EAAED,QAAQS,IAAI,CAAC,OAAO;IAC/F,OAAO;QACL,MAAMC,aAAa;YACjBV;QACF;QACAW,IAAAA,0CAAuB,EAACT,aAAaI,IAAI,EAAEJ,aAAaU,OAAO,EAAGF;IACpE;AACF"}