{"version": 3, "sources": ["../../../src/utils/getOrPromptApplicationId.ts"], "sourcesContent": ["import { GraphQLError } from '@0no-co/graphql.web';\nimport { ExpoConfig, getConfig } from '@expo/config';\nimport { CombinedError } from '@urql/core';\nimport chalk from 'chalk';\n\nimport { memoize } from './fn';\nimport { learnMore } from './link';\nimport { attemptModification } from './modifyConfigAsync';\nimport prompt, { confirmAsync } from './prompts';\nimport {\n  assertValidBundleId,\n  assertValidPackage,\n  getBundleIdWarningAsync,\n  getPackageNameWarningAsync,\n  getSanitizedBundleIdentifier,\n  getSanitizedPackage,\n  validateBundleId,\n  validatePackage,\n  validatePackageWithWarning,\n} from './validateApplicationId';\nimport { AppQuery } from '../api/graphql/queries/AppQuery';\nimport { getSettings } from '../api/user/UserSettings';\nimport * as Log from '../log';\n\nconst debug = require('debug')('expo:app-id') as typeof console.log;\n\nconst ANONYMOUS_USERNAME = 'anonymous';\n\nasync function getRecommendedReverseDomainNameSecondPartAsync(\n  exp: ExpoConfig\n): Promise<string | null> {\n  // Get the cached username.\n  const cachedUsername = getSettings().read().auth?.username;\n  if (cachedUsername) {\n    return cachedUsername;\n  }\n  const easProjectId = exp.extra?.eas?.projectId;\n  if (!easProjectId) {\n    return null;\n  }\n\n  try {\n    const app = await AppQuery.byIdAsync(easProjectId);\n    return app.ownerAccount.name;\n  } catch (e) {\n    if (e instanceof GraphQLError || e instanceof CombinedError) {\n      return null;\n    }\n    throw e;\n  }\n}\n\nconst NO_BUNDLE_ID_MESSAGE = `Project must have a \\`ios.bundleIdentifier\\` set in the Expo config (app.json or app.config.js).`;\n\nconst NO_PACKAGE_MESSAGE = `Project must have a \\`android.package\\` set in the Expo config (app.json or app.config.js).`;\n\n/**\n * Get the bundle identifier from the Expo config or prompt the user to choose a new bundle identifier.\n * Prompted value will be validated against the App Store and a local regex.\n * If the project Expo config is a static JSON file, the bundle identifier will be updated in the config automatically.\n */\nexport async function getOrPromptForBundleIdentifierAsync(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.ios?.bundleIdentifier;\n  if (current) {\n    assertValidBundleId(current);\n    return current;\n  }\n\n  return promptForBundleIdWithInitialAsync(\n    projectRoot,\n    exp,\n    await getRecommendedBundleIdAsync(exp)\n  );\n}\n\nconst memoLog = memoize(Log.log);\n\nasync function promptForBundleIdWithInitialAsync(\n  projectRoot: string,\n  exp: ExpoConfig,\n  bundleIdentifier?: string\n): Promise<string> {\n  if (!bundleIdentifier) {\n    memoLog(\n      chalk`\\n{bold 📝  iOS Bundle Identifier} {dim ${learnMore(\n        'https://expo.fyi/bundle-identifier'\n      )}}\\n`\n    );\n\n    // Prompt the user for the bundle ID.\n    // Even if the project is using a dynamic config we can still\n    // prompt a better error message, recommend a default value, and help the user\n    // validate their custom bundle ID upfront.\n    const { input } = await prompt(\n      {\n        type: 'text',\n        name: 'input',\n        // The Apple helps people know this isn't an EAS feature.\n        message: `What would you like your iOS bundle identifier to be?`,\n        validate: validateBundleId,\n      },\n      {\n        nonInteractiveHelp: NO_BUNDLE_ID_MESSAGE,\n      }\n    );\n    bundleIdentifier = input as string;\n  }\n\n  // Warn the user if the bundle ID is already in use.\n  const warning = await getBundleIdWarningAsync(bundleIdentifier);\n\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Bundle ID prompt to try again.\n    return await promptForBundleIdWithInitialAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  if (\n    await attemptModification(\n      projectRoot,\n      {\n        ios: { ...(exp.ios || {}), bundleIdentifier },\n      },\n      { ios: { bundleIdentifier } }\n    )\n  ) {\n    Log.log(chalk.gray`\\u203A Apple bundle identifier: ${bundleIdentifier}`);\n  }\n\n  return bundleIdentifier;\n}\n\nasync function warnAndConfirmAsync(warning: string): Promise<boolean> {\n  Log.log();\n  Log.warn(warning);\n  Log.log();\n  if (\n    !(await confirmAsync({\n      message: `Continue?`,\n      initial: true,\n    }))\n  ) {\n    return false;\n  }\n  return true;\n}\n\n// Recommend a bundle identifier based on the account name of the owner of the project and project slug.\nasync function getRecommendedBundleIdAsync(exp: ExpoConfig): Promise<string | undefined> {\n  const possibleIdFromAndroid = exp.android?.package\n    ? getSanitizedBundleIdentifier(exp.android.package)\n    : undefined;\n  // Attempt to use the android package name first since it's convenient to have them aligned.\n  if (possibleIdFromAndroid && validateBundleId(possibleIdFromAndroid)) {\n    return possibleIdFromAndroid;\n  } else {\n    const recommendedReverseDomainNameSecondPart =\n      (await getRecommendedReverseDomainNameSecondPartAsync(exp)) ?? ANONYMOUS_USERNAME;\n    const possibleId = getSanitizedBundleIdentifier(\n      `com.${recommendedReverseDomainNameSecondPart}.${exp.slug}`\n    );\n    if (validateBundleId(possibleId)) {\n      return possibleId;\n    }\n  }\n\n  return undefined;\n}\n\n// Recommend a package name based on the account name of the owner of the project and project slug.\nasync function getRecommendedPackageNameAsync(exp: ExpoConfig): Promise<string | undefined> {\n  const possibleIdFromApple = exp.ios?.bundleIdentifier\n    ? getSanitizedPackage(exp.ios.bundleIdentifier)\n    : undefined;\n\n  // Attempt to use the ios bundle id first since it's convenient to have them aligned.\n  if (possibleIdFromApple && validatePackage(possibleIdFromApple)) {\n    return possibleIdFromApple;\n  } else {\n    const recommendedReverseDomainNameSecondPart =\n      (await getRecommendedReverseDomainNameSecondPartAsync(exp)) ?? ANONYMOUS_USERNAME;\n\n    const possibleId = getSanitizedPackage(\n      `com.${recommendedReverseDomainNameSecondPart}.${exp.slug}`\n    );\n    if (validatePackage(possibleId)) {\n      return possibleId;\n    } else {\n      debug(\n        `Recommended package name is invalid: \"${possibleId}\" (owner: ${recommendedReverseDomainNameSecondPart}, slug: ${exp.slug})`\n      );\n    }\n  }\n  return undefined;\n}\n\n/**\n * Get the package name from the Expo config or prompt the user to choose a new package name.\n * Prompted value will be validated against the Play Store and a local regex.\n * If the project Expo config is a static JSON file, the package name will be updated in the config automatically.\n */\nexport async function getOrPromptForPackageAsync(\n  projectRoot: string,\n  exp: ExpoConfig = getConfig(projectRoot).exp\n): Promise<string> {\n  const current = exp.android?.package;\n  if (current) {\n    assertValidPackage(current);\n    return current;\n  }\n\n  return await promptForPackageAsync(projectRoot, exp);\n}\n\nasync function promptForPackageAsync(projectRoot: string, exp: ExpoConfig): Promise<string> {\n  return promptForPackageWithInitialAsync(\n    projectRoot,\n    exp,\n    await getRecommendedPackageNameAsync(exp)\n  );\n}\n\nasync function promptForPackageWithInitialAsync(\n  projectRoot: string,\n  exp: ExpoConfig,\n  packageName?: string\n): Promise<string> {\n  if (!packageName) {\n    memoLog(\n      chalk`\\n{bold 📝  Android package} {dim ${learnMore('https://expo.fyi/android-package')}}\\n`\n    );\n\n    // Prompt the user for the android package.\n    // Even if the project is using a dynamic config we can still\n    // prompt a better error message, recommend a default value, and help the user\n    // validate their custom android package upfront.\n    const { input } = await prompt(\n      {\n        type: 'text',\n        name: 'input',\n        message: `What would you like your Android package name to be?`,\n        validate: validatePackageWithWarning,\n      },\n      {\n        nonInteractiveHelp: NO_PACKAGE_MESSAGE,\n      }\n    );\n    packageName = input as string;\n  }\n\n  // Warn the user if the package name is already in use.\n  const warning = await getPackageNameWarningAsync(packageName);\n  if (warning && !(await warnAndConfirmAsync(warning))) {\n    // Cycle the Package name prompt to try again.\n    return promptForPackageWithInitialAsync(projectRoot, exp);\n  }\n\n  // Apply the changes to the config.\n  if (\n    await attemptModification(\n      projectRoot,\n      {\n        android: { ...(exp.android || {}), package: packageName },\n      },\n      {\n        android: { package: packageName },\n      }\n    )\n  ) {\n    Log.log(chalk.gray`\\u203A Android package name: ${packageName}`);\n  }\n\n  return packageName;\n}\n"], "names": ["getOrPromptForBundleIdentifierAsync", "getOrPromptForPackageAsync", "debug", "require", "ANONYMOUS_USERNAME", "getRecommendedReverseDomainNameSecondPartAsync", "exp", "getSettings", "cachedUsername", "read", "auth", "username", "easProjectId", "extra", "eas", "projectId", "app", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "byIdAsync", "ownerAccount", "name", "e", "GraphQLError", "CombinedError", "NO_BUNDLE_ID_MESSAGE", "NO_PACKAGE_MESSAGE", "projectRoot", "getConfig", "current", "ios", "bundleIdentifier", "assertValidBundleId", "promptForBundleIdWithInitialAsync", "getRecommendedBundleIdAsync", "memoLog", "memoize", "Log", "log", "chalk", "learnMore", "input", "prompt", "type", "message", "validate", "validateBundleId", "nonInteractiveHelp", "warning", "getBundleIdWarningAsync", "warnAndConfirmAsync", "attemptModification", "gray", "warn", "<PERSON><PERSON><PERSON>", "initial", "possibleIdFromAndroid", "android", "package", "getSanitizedBundleIdentifier", "undefined", "recommendedReverseDomainNameSecondPart", "possibleId", "slug", "getRecommendedPackageNameAsync", "possibleIdFromApple", "getSanitizedPackage", "validatePackage", "assertValidPackage", "promptForPackageAsync", "promptForPackageWithInitialAsync", "packageName", "validatePackageWithWarning", "getPackageNameWarningAsync"], "mappings": ";;;;;;;;;;;IA6DsBA,mCAAmC;eAAnCA;;IA+IAC,0BAA0B;eAA1BA;;;;yBA5MO;;;;;;;yBACS;;;;;;;yBACR;;;;;;;gEACZ;;;;;;oBAEM;sBACE;mCACU;iEACC;uCAW9B;0BACkB;8BACG;6DACP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAErB,MAAMC,QAAQC,QAAQ,SAAS;AAE/B,MAAMC,qBAAqB;AAE3B,eAAeC,+CACbC,GAAe;QAGQC,wBAIFD,gBAAAA;IALrB,2BAA2B;IAC3B,MAAME,kBAAiBD,yBAAAA,IAAAA,yBAAW,IAAGE,IAAI,GAAGC,IAAI,qBAAzBH,uBAA2BI,QAAQ;IAC1D,IAAIH,gBAAgB;QAClB,OAAOA;IACT;IACA,MAAMI,gBAAeN,aAAAA,IAAIO,KAAK,sBAATP,iBAAAA,WAAWQ,GAAG,qBAAdR,eAAgBS,SAAS;IAC9C,IAAI,CAACH,cAAc;QACjB,OAAO;IACT;IAEA,IAAI;QACF,MAAMI,MAAM,MAAMC,kBAAQ,CAACC,SAAS,CAACN;QACrC,OAAOI,IAAIG,YAAY,CAACC,IAAI;IAC9B,EAAE,OAAOC,GAAG;QACV,IAAIA,aAAaC,0BAAY,IAAID,aAAaE,qBAAa,EAAE;YAC3D,OAAO;QACT;QACA,MAAMF;IACR;AACF;AAEA,MAAMG,uBAAuB,CAAC,gGAAgG,CAAC;AAE/H,MAAMC,qBAAqB,CAAC,2FAA2F,CAAC;AAOjH,eAAezB,oCACpB0B,WAAmB,EACnBpB,MAAkBqB,IAAAA,mBAAS,EAACD,aAAapB,GAAG;QAE5BA;IAAhB,MAAMsB,WAAUtB,WAAAA,IAAIuB,GAAG,qBAAPvB,SAASwB,gBAAgB;IACzC,IAAIF,SAAS;QACXG,IAAAA,0CAAmB,EAACH;QACpB,OAAOA;IACT;IAEA,OAAOI,kCACLN,aACApB,KACA,MAAM2B,4BAA4B3B;AAEtC;AAEA,MAAM4B,UAAUC,IAAAA,WAAO,EAACC,KAAIC,GAAG;AAE/B,eAAeL,kCACbN,WAAmB,EACnBpB,GAAe,EACfwB,gBAAyB;IAEzB,IAAI,CAACA,kBAAkB;QACrBI,QACEI,IAAAA,gBAAK,CAAA,CAAC,wCAAwC,EAAEC,IAAAA,eAAS,EACvD,sCACA,GAAG,CAAC;QAGR,qCAAqC;QACrC,6DAA6D;QAC7D,8EAA8E;QAC9E,2CAA2C;QAC3C,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAMC,IAAAA,gBAAM,EAC5B;YACEC,MAAM;YACNtB,MAAM;YACN,yDAAyD;YACzDuB,SAAS,CAAC,qDAAqD,CAAC;YAChEC,UAAUC,uCAAgB;QAC5B,GACA;YACEC,oBAAoBtB;QACtB;QAEFM,mBAAmBU;IACrB;IAEA,oDAAoD;IACpD,MAAMO,UAAU,MAAMC,IAAAA,8CAAuB,EAAClB;IAE9C,IAAIiB,WAAW,CAAE,MAAME,oBAAoBF,UAAW;QACpD,2CAA2C;QAC3C,OAAO,MAAMf,kCAAkCN,aAAapB;IAC9D;IAEA,mCAAmC;IACnC,IACE,MAAM4C,IAAAA,sCAAmB,EACvBxB,aACA;QACEG,KAAK;YAAE,GAAIvB,IAAIuB,GAAG,IAAI,CAAC,CAAC;YAAGC;QAAiB;IAC9C,GACA;QAAED,KAAK;YAAEC;QAAiB;IAAE,IAE9B;QACAM,KAAIC,GAAG,CAACC,gBAAK,CAACa,IAAI,CAAC,gCAAgC,EAAErB,iBAAiB,CAAC;IACzE;IAEA,OAAOA;AACT;AAEA,eAAemB,oBAAoBF,OAAe;IAChDX,KAAIC,GAAG;IACPD,KAAIgB,IAAI,CAACL;IACTX,KAAIC,GAAG;IACP,IACE,CAAE,MAAMgB,IAAAA,qBAAY,EAAC;QACnBV,SAAS,CAAC,SAAS,CAAC;QACpBW,SAAS;IACX,IACA;QACA,OAAO;IACT;IACA,OAAO;AACT;AAEA,wGAAwG;AACxG,eAAerB,4BAA4B3B,GAAe;QAC1BA;IAA9B,MAAMiD,wBAAwBjD,EAAAA,eAAAA,IAAIkD,OAAO,qBAAXlD,aAAamD,OAAO,IAC9CC,IAAAA,mDAA4B,EAACpD,IAAIkD,OAAO,CAACC,OAAO,IAChDE;IACJ,4FAA4F;IAC5F,IAAIJ,yBAAyBV,IAAAA,uCAAgB,EAACU,wBAAwB;QACpE,OAAOA;IACT,OAAO;QACL,MAAMK,yCACJ,AAAC,MAAMvD,+CAA+CC,QAASF;QACjE,MAAMyD,aAAaH,IAAAA,mDAA4B,EAC7C,CAAC,IAAI,EAAEE,uCAAuC,CAAC,EAAEtD,IAAIwD,IAAI,EAAE;QAE7D,IAAIjB,IAAAA,uCAAgB,EAACgB,aAAa;YAChC,OAAOA;QACT;IACF;IAEA,OAAOF;AACT;AAEA,mGAAmG;AACnG,eAAeI,+BAA+BzD,GAAe;QAC/BA;IAA5B,MAAM0D,sBAAsB1D,EAAAA,WAAAA,IAAIuB,GAAG,qBAAPvB,SAASwB,gBAAgB,IACjDmC,IAAAA,0CAAmB,EAAC3D,IAAIuB,GAAG,CAACC,gBAAgB,IAC5C6B;IAEJ,qFAAqF;IACrF,IAAIK,uBAAuBE,IAAAA,sCAAe,EAACF,sBAAsB;QAC/D,OAAOA;IACT,OAAO;QACL,MAAMJ,yCACJ,AAAC,MAAMvD,+CAA+CC,QAASF;QAEjE,MAAMyD,aAAaI,IAAAA,0CAAmB,EACpC,CAAC,IAAI,EAAEL,uCAAuC,CAAC,EAAEtD,IAAIwD,IAAI,EAAE;QAE7D,IAAII,IAAAA,sCAAe,EAACL,aAAa;YAC/B,OAAOA;QACT,OAAO;YACL3D,MACE,CAAC,sCAAsC,EAAE2D,WAAW,UAAU,EAAED,uCAAuC,QAAQ,EAAEtD,IAAIwD,IAAI,CAAC,CAAC,CAAC;QAEhI;IACF;IACA,OAAOH;AACT;AAOO,eAAe1D,2BACpByB,WAAmB,EACnBpB,MAAkBqB,IAAAA,mBAAS,EAACD,aAAapB,GAAG;QAE5BA;IAAhB,MAAMsB,WAAUtB,eAAAA,IAAIkD,OAAO,qBAAXlD,aAAamD,OAAO;IACpC,IAAI7B,SAAS;QACXuC,IAAAA,yCAAkB,EAACvC;QACnB,OAAOA;IACT;IAEA,OAAO,MAAMwC,sBAAsB1C,aAAapB;AAClD;AAEA,eAAe8D,sBAAsB1C,WAAmB,EAAEpB,GAAe;IACvE,OAAO+D,iCACL3C,aACApB,KACA,MAAMyD,+BAA+BzD;AAEzC;AAEA,eAAe+D,iCACb3C,WAAmB,EACnBpB,GAAe,EACfgE,WAAoB;IAEpB,IAAI,CAACA,aAAa;QAChBpC,QACEI,IAAAA,gBAAK,CAAA,CAAC,kCAAkC,EAAEC,IAAAA,eAAS,EAAC,oCAAoC,GAAG,CAAC;QAG9F,2CAA2C;QAC3C,6DAA6D;QAC7D,8EAA8E;QAC9E,iDAAiD;QACjD,MAAM,EAAEC,KAAK,EAAE,GAAG,MAAMC,IAAAA,gBAAM,EAC5B;YACEC,MAAM;YACNtB,MAAM;YACNuB,SAAS,CAAC,oDAAoD,CAAC;YAC/DC,UAAU2B,iDAA0B;QACtC,GACA;YACEzB,oBAAoBrB;QACtB;QAEF6C,cAAc9B;IAChB;IAEA,uDAAuD;IACvD,MAAMO,UAAU,MAAMyB,IAAAA,iDAA0B,EAACF;IACjD,IAAIvB,WAAW,CAAE,MAAME,oBAAoBF,UAAW;QACpD,8CAA8C;QAC9C,OAAOsB,iCAAiC3C,aAAapB;IACvD;IAEA,mCAAmC;IACnC,IACE,MAAM4C,IAAAA,sCAAmB,EACvBxB,aACA;QACE8B,SAAS;YAAE,GAAIlD,IAAIkD,OAAO,IAAI,CAAC,CAAC;YAAGC,SAASa;QAAY;IAC1D,GACA;QACEd,SAAS;YAAEC,SAASa;QAAY;IAClC,IAEF;QACAlC,KAAIC,GAAG,CAACC,gBAAK,CAACa,IAAI,CAAC,6BAA6B,EAAEmB,YAAY,CAAC;IACjE;IAEA,OAAOA;AACT"}