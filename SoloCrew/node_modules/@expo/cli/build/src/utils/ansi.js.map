{"version": 3, "sources": ["../../../src/utils/ansi.ts"], "sourcesContent": ["/** Remove ansi characters from a string and return the sanitized results. */\nexport function stripAnsi(str?: string | null) {\n  if (!str) {\n    return str;\n  }\n  const pattern = [\n    '[\\\\u001B\\\\u009B][[\\\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]+)*|[a-zA-Z\\\\d]+(?:;[-a-zA-Z\\\\d\\\\/#&.:=?%@~_]*)*)?\\\\u0007)',\n    '(?:(?:\\\\d{1,4}(?:;\\\\d{0,4})*)?[\\\\dA-PR-TZcf-ntqry=><~]))',\n  ].join('|');\n\n  return str.replace(new RegExp(pattern, 'g'), '');\n}\n"], "names": ["stripAnsi", "str", "pattern", "join", "replace", "RegExp"], "mappings": "AAAA,2EAA2E;;;;+BAC3DA;;;eAAAA;;;AAAT,SAASA,UAAUC,GAAmB;IAC3C,IAAI,CAACA,KAAK;QACR,OAAOA;IACT;IACA,MAAMC,UAAU;QACd;QACA;KACD,CAACC,IAAI,CAAC;IAEP,OAAOF,IAAIG,OAAO,CAAC,IAAIC,OAAOH,SAAS,MAAM;AAC/C"}