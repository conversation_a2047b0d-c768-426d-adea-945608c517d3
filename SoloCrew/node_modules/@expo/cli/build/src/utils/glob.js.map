{"version": 3, "sources": ["../../../src/utils/glob.ts"], "sourcesContent": ["import { glob, globStream, type GlobOptions } from 'glob';\n\n/**\n * Finds all matching files.\n * @deprecated Use `glob` directly instead.\n */\nexport const everyMatchAsync: typeof glob = glob;\n\n/** Bails out early after finding the first matching file. */\nexport function anyMatchAsync(\n  pattern: string,\n  options: Omit<GlobOptions, 'withFileTypes' | 'signal'>\n) {\n  return new Promise<string[]>((resolve, reject) => {\n    const controller = new AbortController();\n\n    globStream(pattern, { ...options, signal: controller.signal })\n      .on('error', (error) => {\n        if (!controller.signal.aborted) {\n          reject(error);\n        }\n      })\n      .once('end', () => resolve([]))\n      .once('data', (file) => {\n        controller.abort();\n        resolve([file]);\n      });\n  });\n}\n"], "names": ["anyMatchAsync", "everyMatchAsync", "glob", "pattern", "options", "Promise", "resolve", "reject", "controller", "AbortController", "globStream", "signal", "on", "error", "aborted", "once", "file", "abort"], "mappings": ";;;;;;;;;;;IASgBA,aAAa;eAAbA;;IAHHC,eAAe;eAAfA;;;;yBANsC;;;;;;AAM5C,MAAMA,kBAA+BC,YAAI;AAGzC,SAASF,cACdG,OAAe,EACfC,OAAsD;IAEtD,OAAO,IAAIC,QAAkB,CAACC,SAASC;QACrC,MAAMC,aAAa,IAAIC;QAEvBC,IAAAA,kBAAU,EAACP,SAAS;YAAE,GAAGC,OAAO;YAAEO,QAAQH,WAAWG,MAAM;QAAC,GACzDC,EAAE,CAAC,SAAS,CAACC;YACZ,IAAI,CAACL,WAAWG,MAAM,CAACG,OAAO,EAAE;gBAC9BP,OAAOM;YACT;QACF,GACCE,IAAI,CAAC,OAAO,IAAMT,QAAQ,EAAE,GAC5BS,IAAI,CAAC,QAAQ,CAACC;YACbR,WAAWS,KAAK;YAChBX,QAAQ;gBAACU;aAAK;QAChB;IACJ;AACF"}