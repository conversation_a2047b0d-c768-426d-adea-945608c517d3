{"version": 3, "sources": ["../../../src/utils/nodeModules.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport fs from 'fs';\nimport path from 'path';\n\nimport { logNewSection } from './ora';\n\nexport async function clearNodeModulesAsync(projectRoot: string) {\n  // This step can take a couple seconds, if the installation logs are enabled (with EXPO_DEBUG) then it\n  // ends up looking odd to see \"Installing JavaScript dependencies\" for ~5 seconds before the logs start showing up.\n  const cleanJsDepsStep = logNewSection('Cleaning JavaScript dependencies');\n  const time = Date.now();\n  // nuke the node modules\n  // TODO: this is substantially slower, we should find a better alternative to ensuring the modules are installed.\n  await fs.promises.rm(path.join(projectRoot, 'node_modules'), { recursive: true, force: true });\n  cleanJsDepsStep.succeed(\n    `Cleaned JavaScript dependencies ${chalk.gray(Date.now() - time + 'ms')}`\n  );\n}\n"], "names": ["clearNodeModulesAsync", "projectRoot", "cleanJsDepsStep", "logNewSection", "time", "Date", "now", "fs", "promises", "rm", "path", "join", "recursive", "force", "succeed", "chalk", "gray"], "mappings": ";;;;+BAMsBA;;;eAAAA;;;;gEANJ;;;;;;;gEACH;;;;;;;gEACE;;;;;;qBAEa;;;;;;AAEvB,eAAeA,sBAAsBC,WAAmB;IAC7D,sGAAsG;IACtG,mHAAmH;IACnH,MAAMC,kBAAkBC,IAAAA,kBAAa,EAAC;IACtC,MAAMC,OAAOC,KAAKC,GAAG;IACrB,wBAAwB;IACxB,iHAAiH;IACjH,MAAMC,aAAE,CAACC,QAAQ,CAACC,EAAE,CAACC,eAAI,CAACC,IAAI,CAACV,aAAa,iBAAiB;QAAEW,WAAW;QAAMC,OAAO;IAAK;IAC5FX,gBAAgBY,OAAO,CACrB,CAAC,gCAAgC,EAAEC,gBAAK,CAACC,IAAI,CAACX,KAAKC,GAAG,KAAKF,OAAO,OAAO;AAE7E"}