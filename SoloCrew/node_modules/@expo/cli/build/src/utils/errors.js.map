{"version": 3, "sources": ["../../../src/utils/errors.ts"], "sourcesContent": ["import { AssertionError } from 'assert';\nimport chalk from 'chalk';\nimport { execSync } from 'child_process';\n\nimport { exit, exception, warn } from '../log';\n\nconst ERROR_PREFIX = 'Error: ';\n\n/**\n * General error, formatted as a message in red text when caught by expo-cli (no stack trace is printed). Should be used in favor of `log.error()` in most cases.\n */\nexport class CommandError extends Error {\n  name = 'CommandError';\n  readonly isCommandError = true;\n\n  constructor(\n    public code: string,\n    message: string = ''\n  ) {\n    super('');\n    // If e.toString() was called to get `message` we don't want it to look\n    // like \"Error: Error:\".\n    if (message.startsWith(ERROR_PREFIX)) {\n      message = message.substring(ERROR_PREFIX.length);\n    }\n\n    this.message = message || code;\n  }\n}\n\nexport class AbortCommandError extends CommandError {\n  constructor() {\n    super('ABORTED', 'Interactive prompt was cancelled.');\n  }\n}\n\n/**\n * Used to end a CLI process without printing a stack trace in the Expo CLI. Should be used in favor of `process.exit`.\n */\nexport class SilentError extends CommandError {\n  constructor(messageOrError?: string | Error) {\n    const message =\n      (typeof messageOrError === 'string' ? messageOrError : messageOrError?.message) ??\n      'This error should fail silently in the CLI';\n    super('SILENT', message);\n    if (typeof messageOrError !== 'string') {\n      // forward the props of the incoming error for tests or processes outside of expo-cli that use expo cli internals.\n      this.stack = messageOrError?.stack ?? this.stack;\n      this.name = messageOrError?.name ?? this.name;\n    }\n  }\n}\n\nexport function logCmdError(error: any): never {\n  if (!(error instanceof Error)) {\n    throw error;\n  }\n  if (error instanceof AbortCommandError || error instanceof SilentError) {\n    // Do nothing, this is used for prompts or other cases that were custom logged.\n    process.exit(0);\n  } else if (\n    error instanceof CommandError ||\n    error instanceof AssertionError ||\n    error.name === 'ApiV2Error' ||\n    error.name === 'ConfigError'\n  ) {\n    // Print the stack trace in debug mode only.\n    exit(error);\n  }\n\n  const errorDetails = error.stack ? '\\n' + chalk.gray(error.stack) : '';\n\n  exit(chalk.red(error.toString()) + errorDetails);\n}\n\n/** This should never be thrown in production. */\nexport class UnimplementedError extends Error {\n  constructor() {\n    super('Unimplemented');\n    this.name = 'UnimplementedError';\n  }\n}\n\n/**\n * Add additional information when EMFILE errors are encountered.\n * These errors originate from Metro's FSEventsWatcher due to `fsevents` going over MacOS system limit.\n * Unfortunately, these limits in macOS are relatively low compared to an average React Native project.\n *\n * @see https://github.com/expo/expo/issues/29083\n * @see https://github.com/facebook/metro/issues/834\n * @see https://github.com/fsevents/fsevents/issues/42#issuecomment-62632234\n */\nfunction handleTooManyOpenFileErrors(error: any) {\n  // Only enable special logging when running on macOS and are running into the `EMFILE` error\n  if ('code' in error && error.code === 'EMFILE' && process.platform === 'darwin') {\n    try {\n      // Try to recover watchman, if it's not installed this will throw\n      execSync('watchman shutdown-server', { stdio: 'ignore' });\n      // NOTE(cedric): this both starts the watchman server and resets all watchers\n      execSync('watchman watch-del-all', { stdio: 'ignore' });\n\n      warn(\n        'Watchman is installed but was likely not enabled when starting Metro, try starting your project again.\\nIf this problem persists, follow the troubleshooting guide of Watchman: https://facebook.github.io/watchman/docs/troubleshooting'\n      );\n    } catch {\n      warn(\n        `Your macOS system limit does not allow enough watchers for Metro, install Watchman instead. Learn more: https://facebook.github.io/watchman/docs/install`\n      );\n    }\n\n    exception(error);\n    process.exit(1);\n  }\n\n  throw error;\n}\n\nprocess.on('uncaughtException', handleTooManyOpenFileErrors);\n"], "names": ["AbortCommandError", "CommandError", "SilentError", "UnimplementedError", "logCmdError", "ERROR_PREFIX", "Error", "constructor", "code", "message", "name", "isCommandError", "startsWith", "substring", "length", "messageOrError", "stack", "error", "process", "exit", "AssertionError", "errorDetails", "chalk", "gray", "red", "toString", "handleTooManyOpenFileErrors", "platform", "execSync", "stdio", "warn", "exception", "on"], "mappings": ";;;;;;;;;;;IA8BaA,iBAAiB;eAAjBA;;IAnBAC,YAAY;eAAZA;;IA4BAC,WAAW;eAAXA;;IAqCAC,kBAAkB;eAAlBA;;IAvBGC,WAAW;eAAXA;;;;yBArDe;;;;;;;gEACb;;;;;;;yBACO;;;;;;qBAEa;;;;;;AAEtC,MAAMC,eAAe;AAKd,MAAMJ,qBAAqBK;IAIhCC,YACE,AAAOC,IAAY,EACnBC,UAAkB,EAAE,CACpB;QACA,KAAK,CAAC,UAHCD,OAAAA,WAJTE,OAAO,qBACEC,iBAAiB;QAOxB,uEAAuE;QACvE,wBAAwB;QACxB,IAAIF,QAAQG,UAAU,CAACP,eAAe;YACpCI,UAAUA,QAAQI,SAAS,CAACR,aAAaS,MAAM;QACjD;QAEA,IAAI,CAACL,OAAO,GAAGA,WAAWD;IAC5B;AACF;AAEO,MAAMR,0BAA0BC;IACrCM,aAAc;QACZ,KAAK,CAAC,WAAW;IACnB;AACF;AAKO,MAAML,oBAAoBD;IAC/BM,YAAYQ,cAA+B,CAAE;QAC3C,MAAMN,UACJ,AAAC,CAAA,OAAOM,mBAAmB,WAAWA,iBAAiBA,kCAAAA,eAAgBN,OAAO,AAAD,KAC7E;QACF,KAAK,CAAC,UAAUA;QAChB,IAAI,OAAOM,mBAAmB,UAAU;YACtC,kHAAkH;YAClH,IAAI,CAACC,KAAK,GAAGD,CAAAA,kCAAAA,eAAgBC,KAAK,KAAI,IAAI,CAACA,KAAK;YAChD,IAAI,CAACN,IAAI,GAAGK,CAAAA,kCAAAA,eAAgBL,IAAI,KAAI,IAAI,CAACA,IAAI;QAC/C;IACF;AACF;AAEO,SAASN,YAAYa,KAAU;IACpC,IAAI,CAAEA,CAAAA,iBAAiBX,KAAI,GAAI;QAC7B,MAAMW;IACR;IACA,IAAIA,iBAAiBjB,qBAAqBiB,iBAAiBf,aAAa;QACtE,+EAA+E;QAC/EgB,QAAQC,IAAI,CAAC;IACf,OAAO,IACLF,iBAAiBhB,gBACjBgB,iBAAiBG,wBAAc,IAC/BH,MAAMP,IAAI,KAAK,gBACfO,MAAMP,IAAI,KAAK,eACf;QACA,4CAA4C;QAC5CS,IAAAA,SAAI,EAACF;IACP;IAEA,MAAMI,eAAeJ,MAAMD,KAAK,GAAG,OAAOM,gBAAK,CAACC,IAAI,CAACN,MAAMD,KAAK,IAAI;IAEpEG,IAAAA,SAAI,EAACG,gBAAK,CAACE,GAAG,CAACP,MAAMQ,QAAQ,MAAMJ;AACrC;AAGO,MAAMlB,2BAA2BG;IACtCC,aAAc;QACZ,KAAK,CAAC;QACN,IAAI,CAACG,IAAI,GAAG;IACd;AACF;AAEA;;;;;;;;CAQC,GACD,SAASgB,4BAA4BT,KAAU;IAC7C,4FAA4F;IAC5F,IAAI,UAAUA,SAASA,MAAMT,IAAI,KAAK,YAAYU,QAAQS,QAAQ,KAAK,UAAU;QAC/E,IAAI;YACF,iEAAiE;YACjEC,IAAAA,yBAAQ,EAAC,4BAA4B;gBAAEC,OAAO;YAAS;YACvD,6EAA6E;YAC7ED,IAAAA,yBAAQ,EAAC,0BAA0B;gBAAEC,OAAO;YAAS;YAErDC,IAAAA,SAAI,EACF;QAEJ,EAAE,OAAM;YACNA,IAAAA,SAAI,EACF,CAAC,wJAAwJ,CAAC;QAE9J;QAEAC,IAAAA,cAAS,EAACd;QACVC,QAAQC,IAAI,CAAC;IACf;IAEA,MAAMF;AACR;AAEAC,QAAQc,EAAE,CAAC,qBAAqBN"}