{"version": 3, "sources": ["../../../src/utils/interactive.ts"], "sourcesContent": ["import { env } from './env';\n\n/** @returns `true` if the process is interactive. */\nexport function isInteractive(): boolean {\n  return !env.CI && process.stdout.isTTY;\n}\n"], "names": ["isInteractive", "env", "CI", "process", "stdout", "isTTY"], "mappings": ";;;;+BAGgBA;;;eAAAA;;;qBAHI;AAGb,SAASA;IACd,OAAO,CAACC,QAAG,CAACC,EAAE,IAAIC,QAAQC,MAAM,CAACC,KAAK;AACxC"}