{"version": 3, "sources": ["../../../src/utils/createTempPath.ts"], "sourcesContent": ["import { randomBytes } from 'crypto';\nimport fs from 'fs';\nimport os from 'os';\nimport path from 'path';\n\nconst uniqueTempPath = (): string => {\n  return path.join(os.tmpdir(), randomBytes(16).toString('hex'));\n};\n\n// Functionally equivalent to: https://github.com/sindresorhus/tempy/blob/943ade0c935367117adbe2b690516ebc94139c6d/index.js#L43-L47\nexport function createTempDirectoryPath(): string {\n  const directory = uniqueTempPath();\n  fs.mkdirSync(directory);\n  return directory;\n}\n\n// Functionally equivalent to: https://github.com/sindresorhus/tempy/blob/943ade0c935367117adbe2b690516ebc94139c6d/index.js#L25-L39\nexport function createTempFilePath(name = ''): string {\n  if (name) {\n    return path.join(createTempDirectoryPath(), name);\n  } else {\n    return uniqueTempPath();\n  }\n}\n"], "names": ["createTempDirectoryPath", "createTempFilePath", "uniqueTempPath", "path", "join", "os", "tmpdir", "randomBytes", "toString", "directory", "fs", "mkdirSync", "name"], "mappings": ";;;;;;;;;;;IAUgBA,uBAAuB;eAAvBA;;IAOAC,kBAAkB;eAAlBA;;;;yBAjBY;;;;;;;gEACb;;;;;;;gEACA;;;;;;;gEACE;;;;;;;;;;;AAEjB,MAAMC,iBAAiB;IACrB,OAAOC,eAAI,CAACC,IAAI,CAACC,aAAE,CAACC,MAAM,IAAIC,IAAAA,qBAAW,EAAC,IAAIC,QAAQ,CAAC;AACzD;AAGO,SAASR;IACd,MAAMS,YAAYP;IAClBQ,aAAE,CAACC,SAAS,CAACF;IACb,OAAOA;AACT;AAGO,SAASR,mBAAmBW,OAAO,EAAE;IAC1C,IAAIA,MAAM;QACR,OAAOT,eAAI,CAACC,IAAI,CAACJ,2BAA2BY;IAC9C,OAAO;QACL,OAAOV;IACT;AACF"}