{"version": 3, "sources": ["../../../src/utils/terminal.ts"], "sourcesContent": ["/** @returns the environment variable indicating the default terminal program to use. */\nexport function getUserTerminal(): string | undefined {\n  return (\n    process.env.REACT_TERMINAL ||\n    (process.platform === 'darwin' ? process.env.TERM_PROGRAM : process.env.TERM)\n  );\n}\n"], "names": ["getUserTerminal", "process", "env", "REACT_TERMINAL", "platform", "TERM_PROGRAM", "TERM"], "mappings": "AAAA,sFAAsF;;;;+BACtEA;;;eAAAA;;;AAAT,SAASA;IACd,OACEC,QAAQC,GAAG,CAACC,cAAc,IACzBF,CAAAA,QAAQG,QAAQ,KAAK,WAAWH,QAAQC,GAAG,CAACG,YAAY,GAAGJ,QAAQC,GAAG,CAACI,IAAI,AAAD;AAE/E"}