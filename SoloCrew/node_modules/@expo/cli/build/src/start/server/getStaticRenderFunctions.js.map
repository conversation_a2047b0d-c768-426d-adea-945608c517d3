{"version": 3, "sources": ["../../../../src/start/server/getStaticRenderFunctions.ts"], "sourcesContent": ["/**\n * Copyright © 2022 650 Industries.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\nimport { getMetroServerRoot } from '@expo/config/paths';\nimport fs from 'fs';\nimport path from 'path';\nimport requireString from 'require-from-string';\n\nimport { IS_METRO_BUNDLE_ERROR_SYMBOL, logMetroError } from './metro/metroErrorInterface';\nimport { createBundleUrlPath, ExpoMetroOptions } from './middleware/metroOptions';\nimport { augmentLogs } from './serverLogLikeMetro';\nimport { delayAsync } from '../../utils/delay';\nimport { SilentError } from '../../utils/errors';\nimport { toPosixPath } from '../../utils/filePath';\nimport { profile } from '../../utils/profile';\n\nconst debug = require('debug')('expo:start:server:getStaticRenderFunctions') as typeof console.log;\n\n/** The list of input keys will become optional, everything else will remain the same. */\nexport type PickPartial<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;\n\nexport const cachedSourceMaps: Map<string, { url: string; map: string }> = new Map();\n\n// Support unhandled rejections\n// Detect if running in Bun\n\n// @ts-expect-error: This is a global variable that is set by Bun.\nif (!process.isBun) {\n  require('source-map-support').install({\n    retrieveSourceMap(source: string) {\n      if (cachedSourceMaps.has(source)) {\n        return cachedSourceMaps.get(source);\n      }\n      return null;\n    },\n  });\n}\n\nasync function ensureFileInRootDirectory(projectRoot: string, otherFile: string) {\n  // Cannot be accessed using Metro's server API, we need to move the file\n  // into the project root and try again.\n  if (!path.relative(projectRoot, otherFile).startsWith('..' + path.sep)) {\n    return otherFile;\n  }\n\n  // Copy the file into the project to ensure it works in monorepos.\n  // This means the file cannot have any relative imports.\n  const tempDir = path.join(projectRoot, '.expo/static-tmp');\n  await fs.promises.mkdir(tempDir, { recursive: true });\n  const moduleId = path.join(tempDir, path.basename(otherFile));\n  await fs.promises.writeFile(moduleId, await fs.promises.readFile(otherFile, 'utf8'));\n  // Sleep to give watchman time to register the file.\n  await delayAsync(50);\n  return moduleId;\n}\n\nexport async function createMetroEndpointAsync(\n  projectRoot: string,\n  devServerUrl: string,\n  absoluteFilePath: string,\n  props: PickPartial<ExpoMetroOptions, 'mainModuleName' | 'bytecode'>\n): Promise<string> {\n  const root = getMetroServerRoot(projectRoot);\n  const safeOtherFile = await ensureFileInRootDirectory(projectRoot, absoluteFilePath);\n  const serverPath = path.relative(root, safeOtherFile).replace(/\\.[jt]sx?$/, '');\n\n  const urlFragment = createBundleUrlPath({\n    mainModuleName: serverPath,\n    lazy: false,\n    asyncRoutes: false,\n    inlineSourceMap: false,\n    engine: 'hermes',\n    minify: false,\n    bytecode: false,\n    ...props,\n  });\n\n  let url: string;\n  if (devServerUrl) {\n    url = new URL(urlFragment.replace(/^\\//, ''), devServerUrl).toString();\n  } else {\n    url = '/' + urlFragment.replace(/^\\/+/, '');\n  }\n  return url;\n}\n\nexport function evalMetroAndWrapFunctions<T = Record<string, any>>(\n  projectRoot: string,\n  script: string,\n  filename: string,\n  isExporting: boolean\n): T {\n  // TODO: Add back stack trace logic that hides traces from metro-runtime and other internal modules.\n  const contents = evalMetroNoHandling(projectRoot, script, filename);\n\n  if (!contents) {\n    // This can happen if ErrorUtils isn't working correctly on web and failing to throw an error when a module throws.\n    // This is unexpected behavior and should not be pretty formatted, therefore we're avoiding CommandError.\n    throw new Error(\n      '[Expo SSR] Module returned undefined, this could be due to a misconfiguration in Metro error handling'\n    );\n  }\n  // wrap each function with a try/catch that uses Metro's error formatter\n  return Object.keys(contents).reduce((acc, key) => {\n    const fn = contents[key];\n    if (typeof fn !== 'function') {\n      return { ...acc, [key]: fn };\n    }\n\n    acc[key] = async function (...props: any[]) {\n      try {\n        return await fn.apply(this, props);\n      } catch (error: any) {\n        await logMetroError(projectRoot, { error });\n\n        if (isExporting || error[IS_METRO_BUNDLE_ERROR_SYMBOL]) {\n          throw error;\n        } else {\n          // TODO: When does this happen?\n          throw new SilentError(error);\n        }\n      }\n    };\n    return acc;\n  }, {} as any);\n}\n\nexport function evalMetroNoHandling(projectRoot: string, src: string, filename: string) {\n  augmentLogs(projectRoot);\n\n  // NOTE(@kitten): `require-from-string` derives a base path from the filename we pass it,\n  // but doesn't validate that the filename exists. These debug messages should help identify\n  // these problems, if they occur in user projects without reproductions\n  if (!fs.existsSync(path.dirname(filename))) {\n    debug(`evalMetroNoHandling received filename in a directory that does not exist: ${filename}`);\n  } else if (!toPosixPath(path.dirname(filename)).startsWith(toPosixPath(projectRoot))) {\n    debug(`evalMetroNoHandling received filename outside of the project root: ${filename}`);\n  }\n\n  return profile(requireString, 'eval-metro-bundle')(src, filename);\n}\n"], "names": ["cachedSourceMaps", "createMetroEndpointAsync", "evalMetroAndWrapFunctions", "evalMetroNoHandling", "debug", "require", "Map", "process", "isBun", "install", "retrieveSourceMap", "source", "has", "get", "ensureFileInRootDirectory", "projectRoot", "otherFile", "path", "relative", "startsWith", "sep", "tempDir", "join", "fs", "promises", "mkdir", "recursive", "moduleId", "basename", "writeFile", "readFile", "delayAsync", "devServerUrl", "absoluteFilePath", "props", "root", "getMetroServerRoot", "safeOtherFile", "serverPath", "replace", "urlFragment", "createBundleUrlPath", "mainModuleName", "lazy", "asyncRoutes", "inlineSourceMap", "engine", "minify", "bytecode", "url", "URL", "toString", "script", "filename", "isExporting", "contents", "Error", "Object", "keys", "reduce", "acc", "key", "fn", "apply", "error", "logMetroError", "IS_METRO_BUNDLE_ERROR_SYMBOL", "SilentError", "src", "augmentLogs", "existsSync", "dirname", "toPosixPath", "profile", "requireString"], "mappings": "AAAA;;;;;CAKC;;;;;;;;;;;IAmBYA,gBAAgB;eAAhBA;;IAmCSC,wBAAwB;eAAxBA;;IA8BNC,yBAAyB;eAAzBA;;IAyCAC,mBAAmB;eAAnBA;;;;yBA5HmB;;;;;;;gEACpB;;;;;;;gEACE;;;;;;;gEACS;;;;;;qCAEkC;8BACN;oCAC1B;uBACD;wBACC;0BACA;yBACJ;;;;;;AAExB,MAAMC,QAAQC,QAAQ,SAAS;AAKxB,MAAML,mBAA8D,IAAIM;AAE/E,+BAA+B;AAC/B,2BAA2B;AAE3B,kEAAkE;AAClE,IAAI,CAACC,QAAQC,KAAK,EAAE;IAClBH,QAAQ,sBAAsBI,OAAO,CAAC;QACpCC,mBAAkBC,MAAc;YAC9B,IAAIX,iBAAiBY,GAAG,CAACD,SAAS;gBAChC,OAAOX,iBAAiBa,GAAG,CAACF;YAC9B;YACA,OAAO;QACT;IACF;AACF;AAEA,eAAeG,0BAA0BC,WAAmB,EAAEC,SAAiB;IAC7E,wEAAwE;IACxE,uCAAuC;IACvC,IAAI,CAACC,eAAI,CAACC,QAAQ,CAACH,aAAaC,WAAWG,UAAU,CAAC,OAAOF,eAAI,CAACG,GAAG,GAAG;QACtE,OAAOJ;IACT;IAEA,kEAAkE;IAClE,wDAAwD;IACxD,MAAMK,UAAUJ,eAAI,CAACK,IAAI,CAACP,aAAa;IACvC,MAAMQ,aAAE,CAACC,QAAQ,CAACC,KAAK,CAACJ,SAAS;QAAEK,WAAW;IAAK;IACnD,MAAMC,WAAWV,eAAI,CAACK,IAAI,CAACD,SAASJ,eAAI,CAACW,QAAQ,CAACZ;IAClD,MAAMO,aAAE,CAACC,QAAQ,CAACK,SAAS,CAACF,UAAU,MAAMJ,aAAE,CAACC,QAAQ,CAACM,QAAQ,CAACd,WAAW;IAC5E,oDAAoD;IACpD,MAAMe,IAAAA,iBAAU,EAAC;IACjB,OAAOJ;AACT;AAEO,eAAe1B,yBACpBc,WAAmB,EACnBiB,YAAoB,EACpBC,gBAAwB,EACxBC,KAAmE;IAEnE,MAAMC,OAAOC,IAAAA,2BAAkB,EAACrB;IAChC,MAAMsB,gBAAgB,MAAMvB,0BAA0BC,aAAakB;IACnE,MAAMK,aAAarB,eAAI,CAACC,QAAQ,CAACiB,MAAME,eAAeE,OAAO,CAAC,cAAc;IAE5E,MAAMC,cAAcC,IAAAA,iCAAmB,EAAC;QACtCC,gBAAgBJ;QAChBK,MAAM;QACNC,aAAa;QACbC,iBAAiB;QACjBC,QAAQ;QACRC,QAAQ;QACRC,UAAU;QACV,GAAGd,KAAK;IACV;IAEA,IAAIe;IACJ,IAAIjB,cAAc;QAChBiB,MAAM,IAAIC,IAAIV,YAAYD,OAAO,CAAC,OAAO,KAAKP,cAAcmB,QAAQ;IACtE,OAAO;QACLF,MAAM,MAAMT,YAAYD,OAAO,CAAC,QAAQ;IAC1C;IACA,OAAOU;AACT;AAEO,SAAS/C,0BACda,WAAmB,EACnBqC,MAAc,EACdC,QAAgB,EAChBC,WAAoB;IAEpB,oGAAoG;IACpG,MAAMC,WAAWpD,oBAAoBY,aAAaqC,QAAQC;IAE1D,IAAI,CAACE,UAAU;QACb,mHAAmH;QACnH,yGAAyG;QACzG,MAAM,IAAIC,MACR;IAEJ;IACA,wEAAwE;IACxE,OAAOC,OAAOC,IAAI,CAACH,UAAUI,MAAM,CAAC,CAACC,KAAKC;QACxC,MAAMC,KAAKP,QAAQ,CAACM,IAAI;QACxB,IAAI,OAAOC,OAAO,YAAY;YAC5B,OAAO;gBAAE,GAAGF,GAAG;gBAAE,CAACC,IAAI,EAAEC;YAAG;QAC7B;QAEAF,GAAG,CAACC,IAAI,GAAG,eAAgB,GAAG3B,KAAY;YACxC,IAAI;gBACF,OAAO,MAAM4B,GAAGC,KAAK,CAAC,IAAI,EAAE7B;YAC9B,EAAE,OAAO8B,OAAY;gBACnB,MAAMC,IAAAA,kCAAa,EAAClD,aAAa;oBAAEiD;gBAAM;gBAEzC,IAAIV,eAAeU,KAAK,CAACE,iDAA4B,CAAC,EAAE;oBACtD,MAAMF;gBACR,OAAO;oBACL,+BAA+B;oBAC/B,MAAM,IAAIG,mBAAW,CAACH;gBACxB;YACF;QACF;QACA,OAAOJ;IACT,GAAG,CAAC;AACN;AAEO,SAASzD,oBAAoBY,WAAmB,EAAEqD,GAAW,EAAEf,QAAgB;IACpFgB,IAAAA,+BAAW,EAACtD;IAEZ,yFAAyF;IACzF,2FAA2F;IAC3F,uEAAuE;IACvE,IAAI,CAACQ,aAAE,CAAC+C,UAAU,CAACrD,eAAI,CAACsD,OAAO,CAAClB,YAAY;QAC1CjD,MAAM,CAAC,0EAA0E,EAAEiD,UAAU;IAC/F,OAAO,IAAI,CAACmB,IAAAA,qBAAW,EAACvD,eAAI,CAACsD,OAAO,CAAClB,WAAWlC,UAAU,CAACqD,IAAAA,qBAAW,EAACzD,eAAe;QACpFX,MAAM,CAAC,mEAAmE,EAAEiD,UAAU;IACxF;IAEA,OAAOoB,IAAAA,gBAAO,EAACC,4BAAa,EAAE,qBAAqBN,KAAKf;AAC1D"}