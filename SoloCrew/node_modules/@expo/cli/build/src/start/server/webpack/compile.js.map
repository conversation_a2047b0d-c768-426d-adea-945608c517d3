{"version": 3, "sources": ["../../../../../src/start/server/webpack/compile.ts"], "sourcesContent": ["import chalk from 'chalk';\nimport { promisify } from 'util';\nimport type webpack from 'webpack';\n\nimport { formatWebpackMessages } from './formatWebpackMessages';\nimport * as Log from '../../../log';\nimport { CommandError } from '../../../utils/errors';\n\n/** Run the `webpack` compiler and format errors/warnings. */\nexport async function compileAsync(compiler: webpack.Compiler) {\n  const stats = await promisify(compiler.run.bind(compiler))();\n  const { errors, warnings } = formatWebpackMessages(\n    stats.toJson({ all: false, warnings: true, errors: true })\n  );\n  if (errors?.length) {\n    // Only keep the first error. Others are often indicative\n    // of the same problem, but confuse the reader with noise.\n    if (errors.length > 1) {\n      errors.length = 1;\n    }\n    throw new CommandError('WEBPACK_BUNDLE', errors.join('\\n\\n'));\n  }\n  if (warnings?.length) {\n    Log.warn(chalk.yellow('Compiled with warnings\\n'));\n    Log.warn(warnings.join('\\n\\n'));\n  } else {\n    Log.log(chalk.green('Compiled successfully'));\n  }\n\n  return { errors, warnings };\n}\n"], "names": ["compileAsync", "compiler", "stats", "promisify", "run", "bind", "errors", "warnings", "formatWebpackMessages", "to<PERSON><PERSON>", "all", "length", "CommandError", "join", "Log", "warn", "chalk", "yellow", "log", "green"], "mappings": ";;;;+BASsBA;;;eAAAA;;;;gEATJ;;;;;;;yBACQ;;;;;;uCAGY;6DACjB;wBACQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGtB,eAAeA,aAAaC,QAA0B;IAC3D,MAAMC,QAAQ,MAAMC,IAAAA,iBAAS,EAACF,SAASG,GAAG,CAACC,IAAI,CAACJ;IAChD,MAAM,EAAEK,MAAM,EAAEC,QAAQ,EAAE,GAAGC,IAAAA,4CAAqB,EAChDN,MAAMO,MAAM,CAAC;QAAEC,KAAK;QAAOH,UAAU;QAAMD,QAAQ;IAAK;IAE1D,IAAIA,0BAAAA,OAAQK,MAAM,EAAE;QAClB,yDAAyD;QACzD,0DAA0D;QAC1D,IAAIL,OAAOK,MAAM,GAAG,GAAG;YACrBL,OAAOK,MAAM,GAAG;QAClB;QACA,MAAM,IAAIC,oBAAY,CAAC,kBAAkBN,OAAOO,IAAI,CAAC;IACvD;IACA,IAAIN,4BAAAA,SAAUI,MAAM,EAAE;QACpBG,KAAIC,IAAI,CAACC,gBAAK,CAACC,MAAM,CAAC;QACtBH,KAAIC,IAAI,CAACR,SAASM,IAAI,CAAC;IACzB,OAAO;QACLC,KAAII,GAAG,CAACF,gBAAK,CAACG,KAAK,CAAC;IACtB;IAEA,OAAO;QAAEb;QAAQC;IAAS;AAC5B"}