{"version": 3, "sources": ["../../../../src/start/project/dotExpo.ts"], "sourcesContent": ["import JsonFile, { JSONObject } from '@expo/json-file';\nimport fs from 'fs';\nimport path from 'path';\n\n/** Create a set of functions for managing a file in the project's `.expo` directory. */\nexport function createTemporaryProjectFile<T extends JSONObject>(fileName: string, defaults: T) {\n  function getFile(projectRoot: string): JsonFile<T> {\n    const dotExpoDir = ensureDotExpoProjectDirectoryInitialized(projectRoot);\n    return new JsonFile<T>(path.join(dotExpoDir, fileName));\n  }\n\n  async function readAsync(projectRoot: string): Promise<T> {\n    let projectSettings;\n    try {\n      projectSettings = await getFile(projectRoot).readAsync();\n    } catch {\n      projectSettings = await getFile(projectRoot).writeAsync(defaults);\n    }\n    // Set defaults for any missing fields\n    return { ...defaults, ...projectSettings };\n  }\n\n  async function setAsync(projectRoot: string, json: Partial<T>): Promise<T> {\n    try {\n      return await getFile(projectRoot).mergeAsync(json, {\n        cantReadFileDefault: defaults,\n      });\n    } catch {\n      return await getFile(projectRoot).writeAsync({\n        ...defaults,\n        ...json,\n      });\n    }\n  }\n\n  return {\n    getFile,\n    readAsync,\n    setAsync,\n  };\n}\n\nfunction getDotExpoProjectDirectory(projectRoot: string): string {\n  return path.join(projectRoot, '.expo');\n}\n\nexport function ensureDotExpoProjectDirectoryInitialized(projectRoot: string): string {\n  const dirPath = getDotExpoProjectDirectory(projectRoot);\n  fs.mkdirSync(dirPath, { recursive: true });\n\n  const readmeFilePath = path.resolve(dirPath, 'README.md');\n  if (!fs.existsSync(readmeFilePath)) {\n    fs.writeFileSync(\n      readmeFilePath,\n      `> Why do I have a folder named \".expo\" in my project?\nThe \".expo\" folder is created when an Expo project is started using \"expo start\" command.\n> What do the files contain?\n- \"devices.json\": contains information about devices that have recently opened this project. This is used to populate the \"Development sessions\" list in your development builds.\n- \"settings.json\": contains the server configuration that is used to serve the application manifest.\n> Should I commit the \".expo\" folder?\nNo, you should not share the \".expo\" folder. It does not contain any information that is relevant for other developers working on the project, it is specific to your machine.\nUpon project creation, the \".expo\" folder is already added to your \".gitignore\" file.\n`\n    );\n  }\n  return dirPath;\n}\n"], "names": ["createTemporaryProjectFile", "ensureDotExpoProjectDirectoryInitialized", "fileName", "defaults", "getFile", "projectRoot", "dotExpoDir", "JsonFile", "path", "join", "readAsync", "projectSettings", "writeAsync", "setAsync", "json", "mergeAsync", "cantReadFileDefault", "getDotExpoProjectDirectory", "<PERSON><PERSON><PERSON>", "fs", "mkdirSync", "recursive", "readmeFilePath", "resolve", "existsSync", "writeFileSync"], "mappings": ";;;;;;;;;;;IAKgBA,0BAA0B;eAA1BA;;IAyCAC,wCAAwC;eAAxCA;;;;gEA9CqB;;;;;;;gEACtB;;;;;;;gEACE;;;;;;;;;;;AAGV,SAASD,2BAAiDE,QAAgB,EAAEC,QAAW;IAC5F,SAASC,QAAQC,WAAmB;QAClC,MAAMC,aAAaL,yCAAyCI;QAC5D,OAAO,IAAIE,CAAAA,WAAO,SAAC,CAAIC,eAAI,CAACC,IAAI,CAACH,YAAYJ;IAC/C;IAEA,eAAeQ,UAAUL,WAAmB;QAC1C,IAAIM;QACJ,IAAI;YACFA,kBAAkB,MAAMP,QAAQC,aAAaK,SAAS;QACxD,EAAE,OAAM;YACNC,kBAAkB,MAAMP,QAAQC,aAAaO,UAAU,CAACT;QAC1D;QACA,sCAAsC;QACtC,OAAO;YAAE,GAAGA,QAAQ;YAAE,GAAGQ,eAAe;QAAC;IAC3C;IAEA,eAAeE,SAASR,WAAmB,EAAES,IAAgB;QAC3D,IAAI;YACF,OAAO,MAAMV,QAAQC,aAAaU,UAAU,CAACD,MAAM;gBACjDE,qBAAqBb;YACvB;QACF,EAAE,OAAM;YACN,OAAO,MAAMC,QAAQC,aAAaO,UAAU,CAAC;gBAC3C,GAAGT,QAAQ;gBACX,GAAGW,IAAI;YACT;QACF;IACF;IAEA,OAAO;QACLV;QACAM;QACAG;IACF;AACF;AAEA,SAASI,2BAA2BZ,WAAmB;IACrD,OAAOG,eAAI,CAACC,IAAI,CAACJ,aAAa;AAChC;AAEO,SAASJ,yCAAyCI,WAAmB;IAC1E,MAAMa,UAAUD,2BAA2BZ;IAC3Cc,aAAE,CAACC,SAAS,CAACF,SAAS;QAAEG,WAAW;IAAK;IAExC,MAAMC,iBAAiBd,eAAI,CAACe,OAAO,CAACL,SAAS;IAC7C,IAAI,CAACC,aAAE,CAACK,UAAU,CAACF,iBAAiB;QAClCH,aAAE,CAACM,aAAa,CACdH,gBACA,CAAC;;;;;;;;AAQP,CAAC;IAEC;IACA,OAAOJ;AACT"}