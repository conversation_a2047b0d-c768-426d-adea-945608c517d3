{"version": 3, "sources": ["../../../../src/start/interface/KeyPressHandler.ts"], "sourcesContent": ["import * as Log from '../../log';\nimport { logCmdError } from '../../utils/errors';\n\nconst CTRL_C = '\\u0003';\n\nconst debug = require('debug')('expo:start:interface:keyPressHandler') as typeof console.log;\n\n/** An abstract key stroke interceptor. */\nexport class KeyPressHandler {\n  private isInterceptingKeyStrokes = false;\n  private isHandlingKeyPress = false;\n\n  constructor(public onPress: (key: string) => Promise<any>) {}\n\n  /** Start observing interaction pause listeners. */\n  createInteractionListener() {\n    // Support observing prompts.\n    let wasIntercepting = false;\n\n    const listener = ({ pause }: { pause: boolean }) => {\n      if (pause) {\n        // Track if we were already intercepting key strokes before pausing, so we can\n        // resume after pausing.\n        wasIntercepting = this.isInterceptingKeyStrokes;\n        this.stopInterceptingKeyStrokes();\n      } else if (wasIntercepting) {\n        // Only start if we were previously intercepting.\n        this.startInterceptingKeyStrokes();\n      }\n    };\n\n    return listener;\n  }\n\n  private handleKeypress = async (key: string) => {\n    // Prevent sending another event until the previous event has finished.\n    if (this.isHandlingKeyPress && key !== CTRL_C) {\n      return;\n    }\n    this.isHandlingKeyPress = true;\n    try {\n      debug(`Key pressed: ${key}`);\n      await this.onPress(key);\n    } catch (error: any) {\n      await logCmdError(error);\n    } finally {\n      this.isHandlingKeyPress = false;\n    }\n  };\n\n  /** Start intercepting all key strokes and passing them to the input `onPress` method. */\n  startInterceptingKeyStrokes() {\n    if (this.isInterceptingKeyStrokes) {\n      return;\n    }\n    this.isInterceptingKeyStrokes = true;\n    const { stdin } = process;\n    // TODO: This might be here because of an old Node version.\n    if (!stdin.setRawMode) {\n      Log.warn('Using a non-interactive terminal, keyboard commands are disabled.');\n      return;\n    }\n    stdin.setRawMode(true);\n    stdin.resume();\n    stdin.setEncoding('utf8');\n    stdin.on('data', this.handleKeypress);\n  }\n\n  /** Stop intercepting all key strokes. */\n  stopInterceptingKeyStrokes() {\n    if (!this.isInterceptingKeyStrokes) {\n      return;\n    }\n    this.isInterceptingKeyStrokes = false;\n    const { stdin } = process;\n    stdin.removeListener('data', this.handleKeypress);\n    // TODO: This might be here because of an old Node version.\n    if (!stdin.setRawMode) {\n      Log.warn('Using a non-interactive terminal, keyboard commands are disabled.');\n      return;\n    }\n    stdin.setRawMode(false);\n    stdin.resume();\n  }\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "CTRL_C", "debug", "require", "constructor", "onPress", "isInterceptingKeyStrokes", "isHandlingKeyPress", "handleKeypress", "key", "error", "logCmdError", "createInteractionListener", "wasIntercepting", "listener", "pause", "stopInterceptingKeyStrokes", "startInterceptingKeyStrokes", "stdin", "process", "setRawMode", "Log", "warn", "resume", "setEncoding", "on", "removeListener"], "mappings": ";;;;+BAQaA;;;eAAAA;;;6DARQ;wBACO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE5B,MAAMC,SAAS;AAEf,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,MAAMH;IAIXI,YAAY,AAAOC,OAAsC,CAAE;aAAxCA,UAAAA;aAHXC,2BAA2B;aAC3BC,qBAAqB;aAwBrBC,iBAAiB,OAAOC;YAC9B,uEAAuE;YACvE,IAAI,IAAI,CAACF,kBAAkB,IAAIE,QAAQR,QAAQ;gBAC7C;YACF;YACA,IAAI,CAACM,kBAAkB,GAAG;YAC1B,IAAI;gBACFL,MAAM,CAAC,aAAa,EAAEO,KAAK;gBAC3B,MAAM,IAAI,CAACJ,OAAO,CAACI;YACrB,EAAE,OAAOC,OAAY;gBACnB,MAAMC,IAAAA,mBAAW,EAACD;YACpB,SAAU;gBACR,IAAI,CAACH,kBAAkB,GAAG;YAC5B;QACF;IApC4D;IAE5D,iDAAiD,GACjDK,4BAA4B;QAC1B,6BAA6B;QAC7B,IAAIC,kBAAkB;QAEtB,MAAMC,WAAW,CAAC,EAAEC,KAAK,EAAsB;YAC7C,IAAIA,OAAO;gBACT,8EAA8E;gBAC9E,wBAAwB;gBACxBF,kBAAkB,IAAI,CAACP,wBAAwB;gBAC/C,IAAI,CAACU,0BAA0B;YACjC,OAAO,IAAIH,iBAAiB;gBAC1B,iDAAiD;gBACjD,IAAI,CAACI,2BAA2B;YAClC;QACF;QAEA,OAAOH;IACT;IAkBA,uFAAuF,GACvFG,8BAA8B;QAC5B,IAAI,IAAI,CAACX,wBAAwB,EAAE;YACjC;QACF;QACA,IAAI,CAACA,wBAAwB,GAAG;QAChC,MAAM,EAAEY,KAAK,EAAE,GAAGC;QAClB,2DAA2D;QAC3D,IAAI,CAACD,MAAME,UAAU,EAAE;YACrBC,KAAIC,IAAI,CAAC;YACT;QACF;QACAJ,MAAME,UAAU,CAAC;QACjBF,MAAMK,MAAM;QACZL,MAAMM,WAAW,CAAC;QAClBN,MAAMO,EAAE,CAAC,QAAQ,IAAI,CAACjB,cAAc;IACtC;IAEA,uCAAuC,GACvCQ,6BAA6B;QAC3B,IAAI,CAAC,IAAI,CAACV,wBAAwB,EAAE;YAClC;QACF;QACA,IAAI,CAACA,wBAAwB,GAAG;QAChC,MAAM,EAAEY,KAAK,EAAE,GAAGC;QAClBD,MAAMQ,cAAc,CAAC,QAAQ,IAAI,CAAClB,cAAc;QAChD,2DAA2D;QAC3D,IAAI,CAACU,MAAME,UAAU,EAAE;YACrBC,KAAIC,IAAI,CAAC;YACT;QACF;QACAJ,MAAME,UAAU,CAAC;QACjBF,MAAMK,MAAM;IACd;AACF"}