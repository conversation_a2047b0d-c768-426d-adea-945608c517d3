{"version": 3, "sources": ["../../../src/start/startAsync.ts"], "sourcesContent": ["import { getConfig } from '@expo/config';\nimport chalk from 'chalk';\n\nimport { SimulatorAppPrerequisite } from './doctor/apple/SimulatorAppPrerequisite';\nimport { getXcodeVersionAsync } from './doctor/apple/XcodePrerequisite';\nimport { validateDependenciesVersionsAsync } from './doctor/dependencies/validateDependenciesVersions';\nimport { WebSupportProjectPrerequisite } from './doctor/web/WebSupportProjectPrerequisite';\nimport { startInterfaceAsync } from './interface/startInterface';\nimport { Options, resolvePortsAsync } from './resolveOptions';\nimport * as Log from '../log';\nimport { BundlerStartOptions } from './server/BundlerDevServer';\nimport { DevServerManager, MultiBundlerStartOptions } from './server/DevServerManager';\nimport { openPlatformsAsync } from './server/openPlatforms';\nimport { getPlatformBundlers, PlatformBundlers } from './server/platformBundlers';\nimport { env } from '../utils/env';\nimport { isInteractive } from '../utils/interactive';\nimport { profile } from '../utils/profile';\n\nasync function getMultiBundlerStartOptions(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean },\n  platformBundlers: PlatformBundlers\n): Promise<[BundlerStartOptions, MultiBundlerStartOptions]> {\n  const commonOptions: BundlerStartOptions = {\n    mode: options.dev ? 'development' : 'production',\n    devClient: options.devClient,\n    privateKeyPath: options.privateKeyPath ?? undefined,\n    https: options.https,\n    maxWorkers: options.maxWorkers,\n    resetDevServer: options.clear,\n    minify: options.minify,\n    location: {\n      hostType: options.host,\n      scheme: options.scheme,\n    },\n  };\n  const multiBundlerSettings = await resolvePortsAsync(projectRoot, options, settings);\n\n  const optionalBundlers: Partial<PlatformBundlers> = { ...platformBundlers };\n  // In the default case, we don't want to start multiple bundlers since this is\n  // a bit slower. Our priority (for legacy) is native platforms.\n  if (!options.web) {\n    delete optionalBundlers['web'];\n  }\n\n  const bundlers = [...new Set(Object.values(optionalBundlers))];\n  const multiBundlerStartOptions = bundlers.map((bundler) => {\n    const port =\n      bundler === 'webpack' ? multiBundlerSettings.webpackPort : multiBundlerSettings.metroPort;\n    return {\n      type: bundler,\n      options: {\n        ...commonOptions,\n        port,\n      },\n    };\n  });\n\n  return [commonOptions, multiBundlerStartOptions];\n}\n\nexport async function startAsync(\n  projectRoot: string,\n  options: Options,\n  settings: { webOnly?: boolean }\n) {\n  Log.log(chalk.gray(`Starting project at ${projectRoot}`));\n\n  const { exp, pkg } = profile(getConfig)(projectRoot);\n\n  if (exp.platforms?.includes('ios') && process.platform !== 'win32') {\n    // If Xcode could potentially be used, then we should eagerly perform the\n    // assertions since they can take a while on cold boots.\n    getXcodeVersionAsync({ silent: true });\n    SimulatorAppPrerequisite.instance.assertAsync().catch(() => {\n      // noop -- this will be thrown again when the user attempts to open the project.\n    });\n  }\n\n  const platformBundlers = getPlatformBundlers(projectRoot, exp);\n\n  const [defaultOptions, startOptions] = await getMultiBundlerStartOptions(\n    projectRoot,\n    options,\n    settings,\n    platformBundlers\n  );\n\n  const devServerManager = new DevServerManager(projectRoot, defaultOptions);\n\n  // Validations\n\n  if (options.web || settings.webOnly) {\n    await devServerManager.ensureProjectPrerequisiteAsync(WebSupportProjectPrerequisite);\n  }\n\n  // Start the server as soon as possible.\n  await profile(devServerManager.startAsync.bind(devServerManager))(startOptions);\n\n  if (!settings.webOnly) {\n    await devServerManager.watchEnvironmentVariables();\n\n    // After the server starts, we can start attempting to bootstrap TypeScript.\n    await devServerManager.bootstrapTypeScriptAsync();\n  }\n\n  if (!env.EXPO_NO_DEPENDENCY_VALIDATION && !settings.webOnly && !options.devClient) {\n    await profile(validateDependenciesVersionsAsync)(projectRoot, exp, pkg);\n  }\n\n  // Open project on devices.\n  await profile(openPlatformsAsync)(devServerManager, options);\n\n  // Present the Terminal UI.\n  if (isInteractive()) {\n    await profile(startInterfaceAsync)(devServerManager, {\n      platforms: exp.platforms ?? ['ios', 'android', 'web'],\n    });\n  } else {\n    // Display the server location in CI...\n    const url = devServerManager.getDefaultDevServer()?.getDevServerUrl();\n    if (url) {\n      if (env.__EXPO_E2E_TEST) {\n        // Print the URL to stdout for tests\n        console.info(`[__EXPO_E2E_TEST:server] ${JSON.stringify({ url })}`);\n      }\n      Log.log(chalk`Waiting on {underline ${url}}`);\n    }\n  }\n\n  // Final note about closing the server.\n  const logLocation = settings.webOnly ? 'in the browser console' : 'below';\n  Log.log(\n    chalk`Logs for your project will appear ${logLocation}.${\n      isInteractive() ? chalk.dim(` Press Ctrl+C to exit.`) : ''\n    }`\n  );\n}\n"], "names": ["startAsync", "getMultiBundlerStartOptions", "projectRoot", "options", "settings", "platformBundlers", "commonOptions", "mode", "dev", "devClient", "privateKeyPath", "undefined", "https", "maxWorkers", "resetDevServer", "clear", "minify", "location", "hostType", "host", "scheme", "multiBundlerSettings", "resolvePortsAsync", "optionalBundlers", "web", "bundlers", "Set", "Object", "values", "multiBundlerStartOptions", "map", "bundler", "port", "webpackPort", "metroPort", "type", "exp", "Log", "log", "chalk", "gray", "pkg", "profile", "getConfig", "platforms", "includes", "process", "platform", "getXcodeVersionAsync", "silent", "SimulatorAppPrerequisite", "instance", "assertAsync", "catch", "getPlatformBundlers", "defaultOptions", "startOptions", "devServerManager", "DevServerManager", "webOnly", "ensureProjectPrerequisiteAsync", "WebSupportProjectPrerequisite", "bind", "watchEnvironmentVariables", "bootstrapTypeScriptAsync", "env", "EXPO_NO_DEPENDENCY_VALIDATION", "validateDependenciesVersionsAsync", "openPlatformsAsync", "isInteractive", "startInterfaceAsync", "url", "getDefaultDevServer", "getDevServerUrl", "__EXPO_E2E_TEST", "console", "info", "JSON", "stringify", "logLocation", "dim"], "mappings": ";;;;+BA8DsBA;;;eAAAA;;;;yBA9DI;;;;;;;gEACR;;;;;;0CAEuB;mCACJ;8CACa;+CACJ;gCACV;gCACO;6DACtB;kCAEsC;+BACxB;kCACmB;qBAClC;6BACU;yBACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAExB,eAAeC,4BACbC,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B,EAC/BC,gBAAkC;IAElC,MAAMC,gBAAqC;QACzCC,MAAMJ,QAAQK,GAAG,GAAG,gBAAgB;QACpCC,WAAWN,QAAQM,SAAS;QAC5BC,gBAAgBP,QAAQO,cAAc,IAAIC;QAC1CC,OAAOT,QAAQS,KAAK;QACpBC,YAAYV,QAAQU,UAAU;QAC9BC,gBAAgBX,QAAQY,KAAK;QAC7BC,QAAQb,QAAQa,MAAM;QACtBC,UAAU;YACRC,UAAUf,QAAQgB,IAAI;YACtBC,QAAQjB,QAAQiB,MAAM;QACxB;IACF;IACA,MAAMC,uBAAuB,MAAMC,IAAAA,iCAAiB,EAACpB,aAAaC,SAASC;IAE3E,MAAMmB,mBAA8C;QAAE,GAAGlB,gBAAgB;IAAC;IAC1E,8EAA8E;IAC9E,+DAA+D;IAC/D,IAAI,CAACF,QAAQqB,GAAG,EAAE;QAChB,OAAOD,gBAAgB,CAAC,MAAM;IAChC;IAEA,MAAME,WAAW;WAAI,IAAIC,IAAIC,OAAOC,MAAM,CAACL;KAAmB;IAC9D,MAAMM,2BAA2BJ,SAASK,GAAG,CAAC,CAACC;QAC7C,MAAMC,OACJD,YAAY,YAAYV,qBAAqBY,WAAW,GAAGZ,qBAAqBa,SAAS;QAC3F,OAAO;YACLC,MAAMJ;YACN5B,SAAS;gBACP,GAAGG,aAAa;gBAChB0B;YACF;QACF;IACF;IAEA,OAAO;QAAC1B;QAAeuB;KAAyB;AAClD;AAEO,eAAe7B,WACpBE,WAAmB,EACnBC,OAAgB,EAChBC,QAA+B;QAM3BgC;IAJJC,KAAIC,GAAG,CAACC,gBAAK,CAACC,IAAI,CAAC,CAAC,oBAAoB,EAAEtC,aAAa;IAEvD,MAAM,EAAEkC,GAAG,EAAEK,GAAG,EAAE,GAAGC,IAAAA,gBAAO,EAACC,mBAAS,EAAEzC;IAExC,IAAIkC,EAAAA,iBAAAA,IAAIQ,SAAS,qBAAbR,eAAeS,QAAQ,CAAC,WAAUC,QAAQC,QAAQ,KAAK,SAAS;QAClE,yEAAyE;QACzE,wDAAwD;QACxDC,IAAAA,uCAAoB,EAAC;YAAEC,QAAQ;QAAK;QACpCC,kDAAwB,CAACC,QAAQ,CAACC,WAAW,GAAGC,KAAK,CAAC;QACpD,gFAAgF;QAClF;IACF;IAEA,MAAMhD,mBAAmBiD,IAAAA,qCAAmB,EAACpD,aAAakC;IAE1D,MAAM,CAACmB,gBAAgBC,aAAa,GAAG,MAAMvD,4BAC3CC,aACAC,SACAC,UACAC;IAGF,MAAMoD,mBAAmB,IAAIC,kCAAgB,CAACxD,aAAaqD;IAE3D,cAAc;IAEd,IAAIpD,QAAQqB,GAAG,IAAIpB,SAASuD,OAAO,EAAE;QACnC,MAAMF,iBAAiBG,8BAA8B,CAACC,4DAA6B;IACrF;IAEA,wCAAwC;IACxC,MAAMnB,IAAAA,gBAAO,EAACe,iBAAiBzD,UAAU,CAAC8D,IAAI,CAACL,mBAAmBD;IAElE,IAAI,CAACpD,SAASuD,OAAO,EAAE;QACrB,MAAMF,iBAAiBM,yBAAyB;QAEhD,4EAA4E;QAC5E,MAAMN,iBAAiBO,wBAAwB;IACjD;IAEA,IAAI,CAACC,QAAG,CAACC,6BAA6B,IAAI,CAAC9D,SAASuD,OAAO,IAAI,CAACxD,QAAQM,SAAS,EAAE;QACjF,MAAMiC,IAAAA,gBAAO,EAACyB,+DAAiC,EAAEjE,aAAakC,KAAKK;IACrE;IAEA,2BAA2B;IAC3B,MAAMC,IAAAA,gBAAO,EAAC0B,iCAAkB,EAAEX,kBAAkBtD;IAEpD,2BAA2B;IAC3B,IAAIkE,IAAAA,0BAAa,KAAI;QACnB,MAAM3B,IAAAA,gBAAO,EAAC4B,mCAAmB,EAAEb,kBAAkB;YACnDb,WAAWR,IAAIQ,SAAS,IAAI;gBAAC;gBAAO;gBAAW;aAAM;QACvD;IACF,OAAO;YAEOa;QADZ,uCAAuC;QACvC,MAAMc,OAAMd,wCAAAA,iBAAiBe,mBAAmB,uBAApCf,sCAAwCgB,eAAe;QACnE,IAAIF,KAAK;YACP,IAAIN,QAAG,CAACS,eAAe,EAAE;gBACvB,oCAAoC;gBACpCC,QAAQC,IAAI,CAAC,CAAC,yBAAyB,EAAEC,KAAKC,SAAS,CAAC;oBAAEP;gBAAI,IAAI;YACpE;YACAlC,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,sBAAsB,EAAEgC,IAAI,CAAC,CAAC;QAC9C;IACF;IAEA,uCAAuC;IACvC,MAAMQ,cAAc3E,SAASuD,OAAO,GAAG,2BAA2B;IAClEtB,KAAIC,GAAG,CACLC,IAAAA,gBAAK,CAAA,CAAC,kCAAkC,EAAEwC,YAAY,CAAC,EACrDV,IAAAA,0BAAa,MAAK9B,gBAAK,CAACyC,GAAG,CAAC,CAAC,sBAAsB,CAAC,IAAI,GACzD,CAAC;AAEN"}