{"version": 3, "sources": ["../../../../src/start/platforms/DeviceManager.ts"], "sourcesContent": ["import chalk from 'chalk';\n\nimport * as Log from '../../log';\n\n/** An abstract class for interacting with a native device. */\nexport abstract class DeviceManager<IDevice> {\n  constructor(public device: IDevice) {}\n\n  abstract get name(): string;\n\n  abstract get identifier(): string;\n\n  logOpeningUrl(url: string) {\n    Log.log(chalk`\\u203A Opening {underline ${url}} on {bold ${this.name}}`);\n  }\n\n  abstract startAsync(): Promise<IDevice>;\n\n  abstract getAppVersionAsync(\n    applicationId: string,\n    options?: { containerPath?: string }\n  ): Promise<string | null>;\n\n  abstract installAppAsync(binaryPath: string): Promise<void>;\n\n  abstract uninstallAppAsync(applicationId: string): Promise<void>;\n\n  abstract isAppInstalledAndIfSoReturnContainerPathForIOSAsync(\n    applicationId: string\n  ): Promise<boolean | string>;\n\n  abstract openUrlAsync(url: string, options?: { appId?: string }): Promise<void>;\n\n  abstract activateWindowAsync(): Promise<void>;\n\n  abstract ensureExpoGoAsync(sdkVersion: string): Promise<boolean>;\n\n  abstract getExpoGoAppId(): string;\n}\n"], "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "device", "logOpeningUrl", "url", "Log", "log", "chalk", "name"], "mappings": ";;;;+BAKsBA;;;eAAAA;;;;gEALJ;;;;;;6DAEG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGd,MAAeA;IACpBC,YAAY,AAAOC,MAAe,CAAE;aAAjBA,SAAAA;IAAkB;IAMrCC,cAAcC,GAAW,EAAE;QACzBC,KAAIC,GAAG,CAACC,IAAAA,gBAAK,CAAA,CAAC,0BAA0B,EAAEH,IAAI,WAAW,EAAE,IAAI,CAACI,IAAI,CAAC,CAAC,CAAC;IACzE;AAwBF"}