{"version": 3, "sources": ["../../../../../src/start/platforms/android/emulator.ts"], "sourcesContent": ["import spawnAsync from '@expo/spawn-async';\nimport chalk from 'chalk';\nimport { spawn } from 'child_process';\nimport os from 'os';\n\nimport { Device, getAttachedDevicesAsync, isBootAnimationCompleteAsync } from './adb';\nimport * as Log from '../../../log';\nimport { AbortCommandError } from '../../../utils/errors';\nimport { installExitHooks } from '../../../utils/exit';\n\nexport const EMULATOR_MAX_WAIT_TIMEOUT = 60 * 1000 * 3;\n\nexport function whichEmulator(): string {\n  // https://developer.android.com/studio/command-line/variables\n  // TODO: Add ANDROID_SDK_ROOT support as well https://github.com/expo/expo/pull/16516#discussion_r820037917\n  if (process.env.ANDROID_HOME) {\n    return `${process.env.ANDROID_HOME}/emulator/emulator`;\n  }\n  return 'emulator';\n}\n\n/** Returns a list of emulator names. */\nexport async function listAvdsAsync(): Promise<Device[]> {\n  try {\n    const { stdout } = await spawnAsync(whichEmulator(), ['-list-avds']);\n    return (\n      stdout\n        .split(os.EOL)\n        .filter(Boolean)\n        /**\n         * AVD IDs cannot contain spaces. This removes extra info lines from the output. e.g.\n         * \"INFO    | Storing crashdata in: /tmp/android-brent/emu-crash-34.1.18.db\n         */\n        .filter((name) => !name.trim().includes(' '))\n        .map((name) => ({\n          name,\n          type: 'emulator',\n          // unsure from this\n          isBooted: false,\n          isAuthorized: true,\n        }))\n    );\n  } catch {\n    return [];\n  }\n}\n\n/** Start an Android device and wait until it is booted. */\nexport async function startDeviceAsync(\n  device: Pick<Device, 'name'>,\n  {\n    timeout = EMULATOR_MAX_WAIT_TIMEOUT,\n    interval = 1000,\n  }: {\n    /** Time in milliseconds to wait before asserting a timeout error. */\n    timeout?: number;\n    interval?: number;\n  } = {}\n): Promise<Device> {\n  Log.log(`\\u203A Opening emulator ${chalk.bold(device.name)}`);\n\n  // Start a process to open an emulator\n  const emulatorProcess = spawn(\n    whichEmulator(),\n    [\n      `@${device.name}`,\n      // disable animation for faster boot -- this might make it harder to detect if it mounted properly tho\n      //'-no-boot-anim'\n    ],\n    {\n      stdio: 'ignore',\n      detached: true,\n    }\n  );\n\n  emulatorProcess.unref();\n\n  return new Promise<Device>((resolve, reject) => {\n    const waitTimer = setInterval(async () => {\n      try {\n        const bootedDevices = await getAttachedDevicesAsync();\n        const connected = bootedDevices.find(({ name }) => name === device.name);\n        if (connected) {\n          const isBooted = await isBootAnimationCompleteAsync(connected.pid);\n          if (isBooted) {\n            stopWaiting();\n            resolve(connected);\n          }\n        }\n      } catch (error) {\n        stopWaiting();\n        reject(error);\n      }\n    }, interval);\n\n    // Reject command after timeout\n    const maxTimer = setTimeout(() => {\n      const manualCommand = `${whichEmulator()} @${device.name}`;\n      stopWaitingAndReject(\n        `It took too long to start the Android emulator: ${device.name}. You can try starting the emulator manually from the terminal with: ${manualCommand}`\n      );\n    }, timeout);\n\n    const stopWaiting = () => {\n      clearTimeout(maxTimer);\n      clearInterval(waitTimer);\n      removeExitHook();\n    };\n\n    const stopWaitingAndReject = (message: string) => {\n      stopWaiting();\n      reject(new Error(message));\n    };\n\n    const removeExitHook = installExitHooks((signal) => {\n      stopWaiting();\n      emulatorProcess.kill(signal);\n      reject(new AbortCommandError());\n    });\n\n    emulatorProcess.on('error', ({ message }) => stopWaitingAndReject(message));\n\n    emulatorProcess.on('exit', () => {\n      const manualCommand = `${whichEmulator()} @${device.name}`;\n      stopWaitingAndReject(\n        `The emulator (${device.name}) quit before it finished opening. You can try starting the emulator manually from the terminal with: ${manualCommand}`\n      );\n    });\n  });\n}\n"], "names": ["EMULATOR_MAX_WAIT_TIMEOUT", "listAvdsAsync", "startDeviceAsync", "whichEmulator", "process", "env", "ANDROID_HOME", "stdout", "spawnAsync", "split", "os", "EOL", "filter", "Boolean", "name", "trim", "includes", "map", "type", "isBooted", "isAuthorized", "device", "timeout", "interval", "Log", "log", "chalk", "bold", "emulatorProcess", "spawn", "stdio", "detached", "unref", "Promise", "resolve", "reject", "waitTimer", "setInterval", "bootedDevices", "getAttachedDevicesAsync", "connected", "find", "isBootAnimationCompleteAsync", "pid", "stopWaiting", "error", "maxTimer", "setTimeout", "manualCommand", "stopWaitingAndReject", "clearTimeout", "clearInterval", "removeExitHook", "message", "Error", "installExitHooks", "signal", "kill", "AbortCommandError", "on"], "mappings": ";;;;;;;;;;;IAUaA,yBAAyB;eAAzBA;;IAYSC,aAAa;eAAbA;;IA0BAC,gBAAgB;eAAhBA;;IApCNC,aAAa;eAAbA;;;;gEAZO;;;;;;;gEACL;;;;;;;yBACI;;;;;;;gEACP;;;;;;qBAE+D;6DACzD;wBACa;sBACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1B,MAAMH,4BAA4B,KAAK,OAAO;AAE9C,SAASG;IACd,8DAA8D;IAC9D,2GAA2G;IAC3G,IAAIC,QAAQC,GAAG,CAACC,YAAY,EAAE;QAC5B,OAAO,GAAGF,QAAQC,GAAG,CAACC,YAAY,CAAC,kBAAkB,CAAC;IACxD;IACA,OAAO;AACT;AAGO,eAAeL;IACpB,IAAI;QACF,MAAM,EAAEM,MAAM,EAAE,GAAG,MAAMC,IAAAA,qBAAU,EAACL,iBAAiB;YAAC;SAAa;QACnE,OACEI,OACGE,KAAK,CAACC,aAAE,CAACC,GAAG,EACZC,MAAM,CAACC,QACR;;;SAGC,IACAD,MAAM,CAAC,CAACE,OAAS,CAACA,KAAKC,IAAI,GAAGC,QAAQ,CAAC,MACvCC,GAAG,CAAC,CAACH,OAAU,CAAA;gBACdA;gBACAI,MAAM;gBACN,mBAAmB;gBACnBC,UAAU;gBACVC,cAAc;YAChB,CAAA;IAEN,EAAE,OAAM;QACN,OAAO,EAAE;IACX;AACF;AAGO,eAAelB,iBACpBmB,MAA4B,EAC5B,EACEC,UAAUtB,yBAAyB,EACnCuB,WAAW,IAAI,EAKhB,GAAG,CAAC,CAAC;IAENC,KAAIC,GAAG,CAAC,CAAC,wBAAwB,EAAEC,gBAAK,CAACC,IAAI,CAACN,OAAOP,IAAI,GAAG;IAE5D,sCAAsC;IACtC,MAAMc,kBAAkBC,IAAAA,sBAAK,EAC3B1B,iBACA;QACE,CAAC,CAAC,EAAEkB,OAAOP,IAAI,EAAE;KAGlB,EACD;QACEgB,OAAO;QACPC,UAAU;IACZ;IAGFH,gBAAgBI,KAAK;IAErB,OAAO,IAAIC,QAAgB,CAACC,SAASC;QACnC,MAAMC,YAAYC,YAAY;YAC5B,IAAI;gBACF,MAAMC,gBAAgB,MAAMC,IAAAA,4BAAuB;gBACnD,MAAMC,YAAYF,cAAcG,IAAI,CAAC,CAAC,EAAE3B,IAAI,EAAE,GAAKA,SAASO,OAAOP,IAAI;gBACvE,IAAI0B,WAAW;oBACb,MAAMrB,WAAW,MAAMuB,IAAAA,iCAA4B,EAACF,UAAUG,GAAG;oBACjE,IAAIxB,UAAU;wBACZyB;wBACAV,QAAQM;oBACV;gBACF;YACF,EAAE,OAAOK,OAAO;gBACdD;gBACAT,OAAOU;YACT;QACF,GAAGtB;QAEH,+BAA+B;QAC/B,MAAMuB,WAAWC,WAAW;YAC1B,MAAMC,gBAAgB,GAAG7C,gBAAgB,EAAE,EAAEkB,OAAOP,IAAI,EAAE;YAC1DmC,qBACE,CAAC,gDAAgD,EAAE5B,OAAOP,IAAI,CAAC,qEAAqE,EAAEkC,eAAe;QAEzJ,GAAG1B;QAEH,MAAMsB,cAAc;YAClBM,aAAaJ;YACbK,cAAcf;YACdgB;QACF;QAEA,MAAMH,uBAAuB,CAACI;YAC5BT;YACAT,OAAO,IAAImB,MAAMD;QACnB;QAEA,MAAMD,iBAAiBG,IAAAA,sBAAgB,EAAC,CAACC;YACvCZ;YACAhB,gBAAgB6B,IAAI,CAACD;YACrBrB,OAAO,IAAIuB,yBAAiB;QAC9B;QAEA9B,gBAAgB+B,EAAE,CAAC,SAAS,CAAC,EAAEN,OAAO,EAAE,GAAKJ,qBAAqBI;QAElEzB,gBAAgB+B,EAAE,CAAC,QAAQ;YACzB,MAAMX,gBAAgB,GAAG7C,gBAAgB,EAAE,EAAEkB,OAAOP,IAAI,EAAE;YAC1DmC,qBACE,CAAC,cAAc,EAAE5B,OAAOP,IAAI,CAAC,sGAAsG,EAAEkC,eAAe;QAExJ;IACF;AACF"}