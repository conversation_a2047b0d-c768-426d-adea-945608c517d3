{"version": 3, "sources": ["../../../../src/api/user/actions.ts"], "sourcesContent": ["import assert from 'assert';\nimport chalk from 'chalk';\n\nimport { retryUsernamePasswordAuthWithOTPAsync } from './otp';\nimport { Actor, getUserAsync, loginAsync, ssoLoginAsync } from './user';\nimport * as Log from '../../log';\nimport { env } from '../../utils/env';\nimport { CommandError } from '../../utils/errors';\nimport { learnMore } from '../../utils/link';\nimport promptAsync, { Question } from '../../utils/prompts';\nimport { ApiV2Error } from '../rest/client';\n\n/** Show login prompt while prompting for missing credentials. */\nexport async function showLoginPromptAsync({\n  printNewLine = false,\n  otp,\n  ...options\n}: {\n  printNewLine?: boolean;\n  username?: string;\n  password?: string;\n  otp?: string;\n  sso?: boolean | undefined;\n} = {}): Promise<void> {\n  if (env.EXPO_OFFLINE) {\n    throw new CommandError('OFFLINE', 'Cannot authenticate in offline-mode');\n  }\n  const hasCredentials = options.username && options.password;\n  const sso = options.sso;\n\n  if (printNewLine) {\n    Log.log();\n  }\n\n  if (sso) {\n    await ssoLoginAsync();\n    return;\n  }\n\n  Log.log(\n    hasCredentials\n      ? `Logging in to EAS with email or username (exit and run 'npx expo login --help' for other login options)`\n      : `Log in to EAS with email or username (exit and run 'npx expo login --help' for other login options)`\n  );\n\n  let username = options.username;\n  let password = options.password;\n\n  if (!hasCredentials) {\n    const resolved = await promptAsync(\n      [\n        !options.username && {\n          type: 'text',\n          name: 'username',\n          message: 'Email or username',\n        },\n        !options.password && {\n          type: 'password',\n          name: 'password',\n          message: 'Password',\n        },\n      ].filter(Boolean) as Question<string>[],\n      {\n        nonInteractiveHelp: `Use the EXPO_TOKEN environment variable to authenticate in CI (${learnMore(\n          'https://docs.expo.dev/accounts/programmatic-access/'\n        )})`,\n      }\n    );\n    username ??= resolved.username;\n    password ??= resolved.password;\n  }\n  // This is just for the types.\n  assert(username && password);\n\n  try {\n    await loginAsync({\n      username,\n      password,\n      otp,\n    });\n  } catch (e) {\n    if (e instanceof ApiV2Error && e.expoApiV2ErrorCode === 'ONE_TIME_PASSWORD_REQUIRED') {\n      await retryUsernamePasswordAuthWithOTPAsync(\n        username,\n        password,\n        e.expoApiV2ErrorMetadata as any\n      );\n    } else {\n      throw e;\n    }\n  }\n}\n\n/** Ensure the user is logged in, if not, prompt to login. */\nexport async function ensureLoggedInAsync(): Promise<Actor> {\n  let user = await getUserAsync().catch(() => null);\n\n  if (!user) {\n    Log.warn(chalk.yellow`An Expo user account is required to proceed.`);\n    await showLoginPromptAsync({ printNewLine: true });\n    user = await getUserAsync();\n  }\n\n  assert(user, 'User should be logged in');\n  return user;\n}\n"], "names": ["ensureLoggedInAsync", "showLoginPromptAsync", "printNewLine", "otp", "options", "env", "EXPO_OFFLINE", "CommandError", "hasCredentials", "username", "password", "sso", "Log", "log", "ssoLoginAsync", "resolved", "promptAsync", "type", "name", "message", "filter", "Boolean", "nonInteractiveHelp", "learnMore", "assert", "loginAsync", "e", "ApiV2Error", "expoApiV2ErrorCode", "retryUsernamePasswordAuthWithOTPAsync", "expoApiV2ErrorMetadata", "user", "getUserAsync", "catch", "warn", "chalk", "yellow"], "mappings": ";;;;;;;;;;;IA8FsBA,mBAAmB;eAAnBA;;IAjFAC,oBAAoB;eAApBA;;;;gEAbH;;;;;;;gEACD;;;;;;qBAEoC;sBACS;6DAC1C;qBACD;wBACS;sBACH;gEACY;wBACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGpB,eAAeA,qBAAqB,EACzCC,eAAe,KAAK,EACpBC,GAAG,EACH,GAAGC,SAOJ,GAAG,CAAC,CAAC;IACJ,IAAIC,QAAG,CAACC,YAAY,EAAE;QACpB,MAAM,IAAIC,oBAAY,CAAC,WAAW;IACpC;IACA,MAAMC,iBAAiBJ,QAAQK,QAAQ,IAAIL,QAAQM,QAAQ;IAC3D,MAAMC,MAAMP,QAAQO,GAAG;IAEvB,IAAIT,cAAc;QAChBU,KAAIC,GAAG;IACT;IAEA,IAAIF,KAAK;QACP,MAAMG,IAAAA,mBAAa;QACnB;IACF;IAEAF,KAAIC,GAAG,CACLL,iBACI,CAAC,uGAAuG,CAAC,GACzG,CAAC,mGAAmG,CAAC;IAG3G,IAAIC,WAAWL,QAAQK,QAAQ;IAC/B,IAAIC,WAAWN,QAAQM,QAAQ;IAE/B,IAAI,CAACF,gBAAgB;QACnB,MAAMO,WAAW,MAAMC,IAAAA,gBAAW,EAChC;YACE,CAACZ,QAAQK,QAAQ,IAAI;gBACnBQ,MAAM;gBACNC,MAAM;gBACNC,SAAS;YACX;YACA,CAACf,QAAQM,QAAQ,IAAI;gBACnBO,MAAM;gBACNC,MAAM;gBACNC,SAAS;YACX;SACD,CAACC,MAAM,CAACC,UACT;YACEC,oBAAoB,CAAC,+DAA+D,EAAEC,IAAAA,eAAS,EAC7F,uDACA,CAAC,CAAC;QACN;QAEFd,aAAaM,SAASN,QAAQ;QAC9BC,aAAaK,SAASL,QAAQ;IAChC;IACA,8BAA8B;IAC9Bc,IAAAA,iBAAM,EAACf,YAAYC;IAEnB,IAAI;QACF,MAAMe,IAAAA,gBAAU,EAAC;YACfhB;YACAC;YACAP;QACF;IACF,EAAE,OAAOuB,GAAG;QACV,IAAIA,aAAaC,kBAAU,IAAID,EAAEE,kBAAkB,KAAK,8BAA8B;YACpF,MAAMC,IAAAA,0CAAqC,EACzCpB,UACAC,UACAgB,EAAEI,sBAAsB;QAE5B,OAAO;YACL,MAAMJ;QACR;IACF;AACF;AAGO,eAAe1B;IACpB,IAAI+B,OAAO,MAAMC,IAAAA,kBAAY,IAAGC,KAAK,CAAC,IAAM;IAE5C,IAAI,CAACF,MAAM;QACTnB,KAAIsB,IAAI,CAACC,gBAAK,CAACC,MAAM,CAAC,4CAA4C,CAAC;QACnE,MAAMnC,qBAAqB;YAAEC,cAAc;QAAK;QAChD6B,OAAO,MAAMC,IAAAA,kBAAY;IAC3B;IAEAR,IAAAA,iBAAM,EAACO,MAAM;IACb,OAAOA;AACT"}