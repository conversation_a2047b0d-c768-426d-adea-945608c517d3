{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithUserAgent.ts"], "sourcesContent": ["import process from 'node:process';\n\nimport { FetchLike } from './client.types';\nimport { Headers } from '../../utils/fetch';\n\nexport function wrapFetchWithUserAgent(fetch: FetchLike): FetchLike {\n  return (url, init = {}) => {\n    const headers = new Headers(init.headers);\n    // Version is added in the build script\n    headers.append('User-Agent', `expo-cli/${process.env.__EXPO_VERSION}`);\n    init.headers = headers;\n    return fetch(url, init);\n  };\n}\n"], "names": ["wrapFetchWithUserAgent", "fetch", "url", "init", "headers", "Headers", "append", "process", "env", "__EXPO_VERSION"], "mappings": ";;;;+BAKgBA;;;eAAAA;;;;gEALI;;;;;;uBAGI;;;;;;AAEjB,SAASA,uBAAuBC,KAAgB;IACrD,OAAO,CAACC,KAAKC,OAAO,CAAC,CAAC;QACpB,MAAMC,UAAU,IAAIC,cAAO,CAACF,KAAKC,OAAO;QACxC,uCAAuC;QACvCA,QAAQE,MAAM,CAAC,cAAc,CAAC,SAAS,EAAEC,sBAAO,CAACC,GAAG,CAACC,cAAc,EAAE;QACrEN,KAAKC,OAAO,GAAGA;QACf,OAAOH,MAAMC,KAAKC;IACpB;AACF"}