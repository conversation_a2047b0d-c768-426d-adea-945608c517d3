{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithBaseUrl.ts"], "sourcesContent": ["import { URL } from 'url';\n\nimport { FetchLike } from './client.types';\n\n// const debug = require('debug')('expo:api:fetch:base') as typeof console.log;\n\n/**\n * Wrap a fetch function with support for a predefined base URL.\n * This implementation works like the browser fetch, applying the input to a prefix base URL.\n */\nexport function wrapFetchWithBaseUrl(fetch: FetchLike, baseUrl: string): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return (url, init) => {\n    if (typeof url !== 'string') {\n      throw new TypeError('Custom fetch function only accepts a string URL as the first parameter');\n    }\n    const parsed = new URL(url, baseUrl);\n    if (init?.searchParams) {\n      parsed.search = init.searchParams.toString();\n    }\n    return fetch(parsed.toString(), init);\n  };\n}\n"], "names": ["wrapFetchWithBaseUrl", "fetch", "baseUrl", "url", "init", "TypeError", "parsed", "URL", "searchParams", "search", "toString"], "mappings": ";;;;+BAUgBA;;;eAAAA;;;;yBAVI;;;;;;AAUb,SAASA,qBAAqBC,KAAgB,EAAEC,OAAe;IACpE,mFAAmF;IACnF,OAAO,CAACC,KAAKC;QACX,IAAI,OAAOD,QAAQ,UAAU;YAC3B,MAAM,IAAIE,UAAU;QACtB;QACA,MAAMC,SAAS,IAAIC,CAAAA,MAAE,KAAC,CAACJ,KAAKD;QAC5B,IAAIE,wBAAAA,KAAMI,YAAY,EAAE;YACtBF,OAAOG,MAAM,GAAGL,KAAKI,YAAY,CAACE,QAAQ;QAC5C;QACA,OAAOT,MAAMK,OAAOI,QAAQ,IAAIN;IAClC;AACF"}