{"version": 3, "sources": ["../../../../src/api/rest/wrapFetchWithProxy.ts"], "sourcesContent": ["import { EnvHttpProxyAgent } from 'undici';\n\nimport { FetchLike } from './client.types';\nimport { env } from '../../utils/env';\n\nconst debug = require('debug')('expo:api:fetch:proxy') as typeof console.log;\n\n/** Wrap fetch with support for proxies. */\nexport function wrapFetchWithProxy(fetchFunction: FetchLike): FetchLike {\n  // NOTE(EvanBacon): DO NOT RETURN AN ASYNC WRAPPER. THIS BREAKS LOADING INDICATORS.\n  return function fetchWithProxy(url, options = {}) {\n    if (!options.dispatcher && env.HTTP_PROXY) {\n      debug('Using proxy:', env.HTTP_PROXY);\n      options.dispatcher = new EnvHttpProxyAgent();\n    }\n\n    return fetchFunction(url, options);\n  };\n}\n"], "names": ["wrapFetchWithProxy", "debug", "require", "fetchFunction", "fetchWithProxy", "url", "options", "dispatcher", "env", "HTTP_PROXY", "EnvHttpProxyAgent"], "mappings": ";;;;+BAQgBA;;;eAAAA;;;;yBARkB;;;;;;qBAGd;AAEpB,MAAMC,QAAQC,QAAQ,SAAS;AAGxB,SAASF,mBAAmBG,aAAwB;IACzD,mFAAmF;IACnF,OAAO,SAASC,eAAeC,GAAG,EAAEC,UAAU,CAAC,CAAC;QAC9C,IAAI,CAACA,QAAQC,UAAU,IAAIC,QAAG,CAACC,UAAU,EAAE;YACzCR,MAAM,gBAAgBO,QAAG,CAACC,UAAU;YACpCH,QAAQC,UAAU,GAAG,IAAIG,CAAAA,SAAgB,mBAAC;QAC5C;QAEA,OAAOP,cAAcE,KAAKC;IAC5B;AACF"}