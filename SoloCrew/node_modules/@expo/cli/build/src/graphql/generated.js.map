{"version": 3, "sources": ["../../../src/graphql/generated.ts"], "sourcesContent": ["/**\n * eslint-disable\n * This file was generated using GraphQL Codegen\n * Command: yarn generate-graphql-code\n * Run this during development for automatic type generation when editing GraphQL documents\n * For more info and docs, visit https://graphql-code-generator.com/\n */\n\nexport type Maybe<T> = T | null;\nexport type InputMaybe<T> = Maybe<T>;\nexport type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };\nexport type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };\nexport type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };\n/** All built-in and custom scalars, mapped to their actual values */\nexport type Scalars = {\n  ID: string;\n  String: string;\n  Boolean: boolean;\n  Int: number;\n  Float: number;\n  DateTime: any;\n  DevDomainName: any;\n  JSON: any;\n  JSONObject: any;\n  WorkerDeploymentIdentifier: any;\n  WorkerDeploymentRequestID: any;\n};\n\nexport type AccountAppsFilterInput = {\n  searchTerm?: InputMaybe<Scalars['String']>;\n  sortByField: AccountAppsSortByField;\n};\n\nexport enum AccountAppsSortByField {\n  LatestActivityTime = 'LATEST_ACTIVITY_TIME',\n  /**\n   * Name prefers the display name but falls back to full_name with @account/\n   * part stripped.\n   */\n  Name = 'NAME'\n}\n\nexport type AccountDataInput = {\n  name: Scalars['String'];\n};\n\nexport type AccountNotificationSubscriptionInput = {\n  accountId: Scalars['ID'];\n  event: NotificationEvent;\n  type: NotificationType;\n  userId: Scalars['ID'];\n};\n\nexport type AccountSsoConfigurationData = {\n  authProtocol: AuthProtocolType;\n  authProviderIdentifier: AuthProviderIdentifier;\n  clientIdentifier: Scalars['String'];\n  clientSecret: Scalars['String'];\n  issuer: Scalars['String'];\n};\n\nexport enum AccountUploadSessionType {\n  ProfileImageUpload = 'PROFILE_IMAGE_UPLOAD',\n  WorkflowsProjectSources = 'WORKFLOWS_PROJECT_SOURCES'\n}\n\nexport enum ActivityTimelineProjectActivityType {\n  Build = 'BUILD',\n  Submission = 'SUBMISSION',\n  Update = 'UPDATE',\n  Worker = 'WORKER',\n  WorkflowRun = 'WORKFLOW_RUN'\n}\n\nexport type AddUserInput = {\n  audience?: InputMaybe<MailchimpAudience>;\n  email: Scalars['String'];\n  tags?: InputMaybe<Array<MailchimpTag>>;\n};\n\n/** @isDefault: if set, these build credentials will become the default for the Android app. All other build credentials will have their default status set to false. */\nexport type AndroidAppBuildCredentialsInput = {\n  isDefault: Scalars['Boolean'];\n  keystoreId: Scalars['ID'];\n  name: Scalars['String'];\n};\n\nexport type AndroidAppCredentialsFilter = {\n  applicationIdentifier?: InputMaybe<Scalars['String']>;\n  legacyOnly?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport type AndroidAppCredentialsInput = {\n  fcmId?: InputMaybe<Scalars['ID']>;\n  googleServiceAccountKeyForFcmV1Id?: InputMaybe<Scalars['ID']>;\n  googleServiceAccountKeyForSubmissionsId?: InputMaybe<Scalars['ID']>;\n};\n\nexport enum AndroidBuildType {\n  Apk = 'APK',\n  AppBundle = 'APP_BUNDLE',\n  /** @deprecated Use developmentClient option instead. */\n  DevelopmentClient = 'DEVELOPMENT_CLIENT'\n}\n\nexport type AndroidBuilderEnvironmentInput = {\n  bun?: InputMaybe<Scalars['String']>;\n  env?: InputMaybe<Scalars['JSONObject']>;\n  expoCli?: InputMaybe<Scalars['String']>;\n  image?: InputMaybe<Scalars['String']>;\n  ndk?: InputMaybe<Scalars['String']>;\n  node?: InputMaybe<Scalars['String']>;\n  pnpm?: InputMaybe<Scalars['String']>;\n  yarn?: InputMaybe<Scalars['String']>;\n};\n\nexport type AndroidFcmInput = {\n  credential: Scalars['String'];\n  version: AndroidFcmVersion;\n};\n\nexport enum AndroidFcmVersion {\n  Legacy = 'LEGACY',\n  V1 = 'V1'\n}\n\nexport type AndroidJobBuildCredentialsInput = {\n  keystore: AndroidJobKeystoreInput;\n};\n\nexport type AndroidJobInput = {\n  applicationArchivePath?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  artifactPath?: InputMaybe<Scalars['String']>;\n  buildArtifactPaths?: InputMaybe<Array<Scalars['String']>>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  buildType?: InputMaybe<AndroidBuildType>;\n  builderEnvironment?: InputMaybe<AndroidBuilderEnvironmentInput>;\n  cache?: InputMaybe<BuildCacheInput>;\n  customBuildConfig?: InputMaybe<CustomBuildConfigInput>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  experimental?: InputMaybe<Scalars['JSONObject']>;\n  gradleCommand?: InputMaybe<Scalars['String']>;\n  loggerLevel?: InputMaybe<WorkerLoggerLevel>;\n  mode?: InputMaybe<BuildMode>;\n  projectArchive: ProjectArchiveSourceInput;\n  projectRootDirectory: Scalars['String'];\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  secrets?: InputMaybe<AndroidJobSecretsInput>;\n  triggeredBy?: InputMaybe<BuildTrigger>;\n  type: BuildWorkflow;\n  updates?: InputMaybe<BuildUpdatesInput>;\n  username?: InputMaybe<Scalars['String']>;\n  version?: InputMaybe<AndroidJobVersionInput>;\n};\n\nexport type AndroidJobKeystoreInput = {\n  dataBase64: Scalars['String'];\n  keyAlias: Scalars['String'];\n  keyPassword?: InputMaybe<Scalars['String']>;\n  keystorePassword: Scalars['String'];\n};\n\nexport type AndroidJobOverridesInput = {\n  applicationArchivePath?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  artifactPath?: InputMaybe<Scalars['String']>;\n  buildArtifactPaths?: InputMaybe<Array<Scalars['String']>>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  buildType?: InputMaybe<AndroidBuildType>;\n  builderEnvironment?: InputMaybe<AndroidBuilderEnvironmentInput>;\n  cache?: InputMaybe<BuildCacheInput>;\n  customBuildConfig?: InputMaybe<CustomBuildConfigInput>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  experimental?: InputMaybe<Scalars['JSONObject']>;\n  gradleCommand?: InputMaybe<Scalars['String']>;\n  loggerLevel?: InputMaybe<WorkerLoggerLevel>;\n  mode?: InputMaybe<BuildMode>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  secrets?: InputMaybe<AndroidJobSecretsInput>;\n  updates?: InputMaybe<BuildUpdatesInput>;\n  username?: InputMaybe<Scalars['String']>;\n  version?: InputMaybe<AndroidJobVersionInput>;\n};\n\nexport type AndroidJobSecretsInput = {\n  buildCredentials?: InputMaybe<AndroidJobBuildCredentialsInput>;\n  robotAccessToken?: InputMaybe<Scalars['String']>;\n};\n\nexport type AndroidJobVersionInput = {\n  versionCode: Scalars['String'];\n};\n\nexport type AndroidKeystoreInput = {\n  base64EncodedKeystore: Scalars['String'];\n  keyAlias: Scalars['String'];\n  keyPassword?: InputMaybe<Scalars['String']>;\n  keystorePassword: Scalars['String'];\n};\n\nexport enum AndroidKeystoreType {\n  Jks = 'JKS',\n  Pkcs12 = 'PKCS12',\n  Unknown = 'UNKNOWN'\n}\n\nexport type AndroidSubmissionConfigInput = {\n  applicationIdentifier?: InputMaybe<Scalars['String']>;\n  archiveUrl?: InputMaybe<Scalars['String']>;\n  changesNotSentForReview?: InputMaybe<Scalars['Boolean']>;\n  googleServiceAccountKeyId?: InputMaybe<Scalars['String']>;\n  googleServiceAccountKeyJson?: InputMaybe<Scalars['String']>;\n  isVerboseFastlaneEnabled?: InputMaybe<Scalars['Boolean']>;\n  releaseStatus?: InputMaybe<SubmissionAndroidReleaseStatus>;\n  rollout?: InputMaybe<Scalars['Float']>;\n  track: SubmissionAndroidTrack;\n};\n\nexport type AppDataInput = {\n  id: Scalars['ID'];\n  internalDistributionBuildPrivacy?: InputMaybe<AppInternalDistributionBuildPrivacy>;\n  privacy?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppInfoInput = {\n  displayName?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppInput = {\n  accountId: Scalars['ID'];\n  appInfo?: InputMaybe<AppInfoInput>;\n  projectName: Scalars['String'];\n};\n\nexport enum AppInternalDistributionBuildPrivacy {\n  Private = 'PRIVATE',\n  Public = 'PUBLIC'\n}\n\nexport type AppNotificationSubscriptionInput = {\n  appId: Scalars['ID'];\n  event: NotificationEvent;\n  type: NotificationType;\n  userId: Scalars['ID'];\n};\n\nexport enum AppPlatform {\n  Android = 'ANDROID',\n  Ios = 'IOS'\n}\n\nexport enum AppPrivacy {\n  Hidden = 'HIDDEN',\n  Public = 'PUBLIC',\n  Unlisted = 'UNLISTED'\n}\n\nexport enum AppSort {\n  /** Sort by recently published */\n  RecentlyPublished = 'RECENTLY_PUBLISHED',\n  /** Sort by highest trendScore */\n  Viewed = 'VIEWED'\n}\n\nexport type AppStoreConnectApiKeyInput = {\n  appleTeamId?: InputMaybe<Scalars['ID']>;\n  issuerIdentifier: Scalars['String'];\n  keyIdentifier: Scalars['String'];\n  keyP8: Scalars['String'];\n  name?: InputMaybe<Scalars['String']>;\n  roles?: InputMaybe<Array<AppStoreConnectUserRole>>;\n};\n\nexport type AppStoreConnectApiKeyUpdateInput = {\n  appleTeamId?: InputMaybe<Scalars['ID']>;\n};\n\nexport enum AppStoreConnectUserRole {\n  AccessToReports = 'ACCESS_TO_REPORTS',\n  AccountHolder = 'ACCOUNT_HOLDER',\n  Admin = 'ADMIN',\n  AppManager = 'APP_MANAGER',\n  CloudManagedAppDistribution = 'CLOUD_MANAGED_APP_DISTRIBUTION',\n  CloudManagedDeveloperId = 'CLOUD_MANAGED_DEVELOPER_ID',\n  CreateApps = 'CREATE_APPS',\n  CustomerSupport = 'CUSTOMER_SUPPORT',\n  Developer = 'DEVELOPER',\n  Finance = 'FINANCE',\n  ImageManager = 'IMAGE_MANAGER',\n  Marketing = 'MARKETING',\n  ReadOnly = 'READ_ONLY',\n  Sales = 'SALES',\n  Technical = 'TECHNICAL',\n  Unknown = 'UNKNOWN'\n}\n\nexport enum AppUploadSessionType {\n  ProfileImageUpload = 'PROFILE_IMAGE_UPLOAD'\n}\n\nexport type AppVersionInput = {\n  appId: Scalars['ID'];\n  applicationIdentifier: Scalars['String'];\n  buildVersion: Scalars['String'];\n  platform: AppPlatform;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n  storeVersion: Scalars['String'];\n};\n\nexport type AppWithGithubRepositoryInput = {\n  accountId: Scalars['ID'];\n  appInfo?: InputMaybe<AppInfoInput>;\n  installationIdentifier?: InputMaybe<Scalars['String']>;\n  projectName: Scalars['String'];\n};\n\nexport type AppleAppIdentifierInput = {\n  appleTeamId?: InputMaybe<Scalars['ID']>;\n  bundleIdentifier: Scalars['String'];\n  parentAppleAppId?: InputMaybe<Scalars['ID']>;\n};\n\nexport enum AppleDeviceClass {\n  Ipad = 'IPAD',\n  Iphone = 'IPHONE',\n  Mac = 'MAC',\n  Unknown = 'UNKNOWN'\n}\n\nexport type AppleDeviceFilterInput = {\n  appleTeamIdentifier?: InputMaybe<Scalars['String']>;\n  class?: InputMaybe<AppleDeviceClass>;\n  identifier?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppleDeviceInput = {\n  appleTeamId: Scalars['ID'];\n  deviceClass?: InputMaybe<AppleDeviceClass>;\n  enabled?: InputMaybe<Scalars['Boolean']>;\n  identifier: Scalars['String'];\n  model?: InputMaybe<Scalars['String']>;\n  name?: InputMaybe<Scalars['String']>;\n  softwareVersion?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppleDeviceUpdateInput = {\n  name?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppleDistributionCertificateInput = {\n  appleTeamId?: InputMaybe<Scalars['ID']>;\n  certP12: Scalars['String'];\n  certPassword: Scalars['String'];\n  certPrivateSigningKey?: InputMaybe<Scalars['String']>;\n  developerPortalIdentifier?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppleProvisioningProfileInput = {\n  appleProvisioningProfile: Scalars['String'];\n  developerPortalIdentifier?: InputMaybe<Scalars['String']>;\n};\n\nexport type ApplePushKeyInput = {\n  appleTeamId: Scalars['ID'];\n  keyIdentifier: Scalars['String'];\n  keyP8: Scalars['String'];\n};\n\nexport type AppleTeamFilterInput = {\n  appleTeamIdentifier?: InputMaybe<Scalars['String']>;\n};\n\nexport type AppleTeamInput = {\n  appleTeamIdentifier: Scalars['String'];\n  appleTeamName?: InputMaybe<Scalars['String']>;\n  appleTeamType?: InputMaybe<AppleTeamType>;\n};\n\nexport enum AppleTeamType {\n  CompanyOrOrganization = 'COMPANY_OR_ORGANIZATION',\n  Individual = 'INDIVIDUAL',\n  InHouse = 'IN_HOUSE'\n}\n\nexport type AppleTeamUpdateInput = {\n  appleTeamName?: InputMaybe<Scalars['String']>;\n  appleTeamType?: InputMaybe<AppleTeamType>;\n};\n\nexport enum AppsFilter {\n  /** Featured Projects */\n  Featured = 'FEATURED',\n  /** New Projects */\n  New = 'NEW'\n}\n\nexport type AscApiKeyInput = {\n  issuerIdentifier: Scalars['String'];\n  keyIdentifier: Scalars['String'];\n  keyP8: Scalars['String'];\n};\n\nexport enum AssetMetadataStatus {\n  DoesNotExist = 'DOES_NOT_EXIST',\n  Exists = 'EXISTS'\n}\n\nexport type AuditLogExportInput = {\n  accountId: Scalars['ID'];\n  createdAfter: Scalars['String'];\n  createdBefore: Scalars['String'];\n  format: AuditLogsExportFormat;\n  targetEntityMutationType?: InputMaybe<Array<TargetEntityMutationType>>;\n  targetEntityTypeName?: InputMaybe<Array<EntityTypeName>>;\n};\n\nexport type AuditLogFilterInput = {\n  entityTypes?: InputMaybe<Array<EntityTypeName>>;\n  mutationTypes?: InputMaybe<Array<TargetEntityMutationType>>;\n};\n\nexport enum AuditLogsExportFormat {\n  Csv = 'CSV',\n  Json = 'JSON',\n  Jsonl = 'JSONL'\n}\n\nexport enum AuthProtocolType {\n  Oidc = 'OIDC'\n}\n\nexport enum AuthProviderIdentifier {\n  GoogleWs = 'GOOGLE_WS',\n  MsEntraId = 'MS_ENTRA_ID',\n  Okta = 'OKTA',\n  OneLogin = 'ONE_LOGIN',\n  StubIdp = 'STUB_IDP'\n}\n\nexport enum BackgroundJobResultType {\n  AuditLogsExport = 'AUDIT_LOGS_EXPORT',\n  GithubBuild = 'GITHUB_BUILD',\n  UserAuditLogsExport = 'USER_AUDIT_LOGS_EXPORT',\n  Void = 'VOID'\n}\n\nexport enum BackgroundJobState {\n  Failure = 'FAILURE',\n  InProgress = 'IN_PROGRESS',\n  Queued = 'QUEUED',\n  Success = 'SUCCESS'\n}\n\nexport type BranchFilterInput = {\n  searchTerm?: InputMaybe<Scalars['String']>;\n};\n\nexport type BuildAnnotationDataInput = {\n  buildPhase: Scalars['String'];\n  exampleBuildLog?: InputMaybe<Scalars['String']>;\n  internalNotes?: InputMaybe<Scalars['String']>;\n  message: Scalars['String'];\n  regexFlags?: InputMaybe<Scalars['String']>;\n  regexString: Scalars['String'];\n  title: Scalars['String'];\n};\n\nexport type BuildAnnotationFiltersInput = {\n  buildPhases: Array<Scalars['String']>;\n};\n\nexport type BuildCacheInput = {\n  clear?: InputMaybe<Scalars['Boolean']>;\n  disabled?: InputMaybe<Scalars['Boolean']>;\n  key?: InputMaybe<Scalars['String']>;\n  paths?: InputMaybe<Array<Scalars['String']>>;\n};\n\nexport enum BuildCredentialsSource {\n  Local = 'LOCAL',\n  Remote = 'REMOTE'\n}\n\nexport type BuildFilter = {\n  appBuildVersion?: InputMaybe<Scalars['String']>;\n  appIdentifier?: InputMaybe<Scalars['String']>;\n  appVersion?: InputMaybe<Scalars['String']>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  channel?: InputMaybe<Scalars['String']>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  distribution?: InputMaybe<DistributionType>;\n  fingerprintHash?: InputMaybe<Scalars['String']>;\n  gitCommitHash?: InputMaybe<Scalars['String']>;\n  hasFingerprint?: InputMaybe<Scalars['Boolean']>;\n  platform?: InputMaybe<AppPlatform>;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n  sdkVersion?: InputMaybe<Scalars['String']>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n  status?: InputMaybe<BuildStatus>;\n};\n\nexport type BuildFilterInput = {\n  channel?: InputMaybe<Scalars['String']>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  distributions?: InputMaybe<Array<DistributionType>>;\n  fingerprintHash?: InputMaybe<Scalars['String']>;\n  hasFingerprint?: InputMaybe<Scalars['Boolean']>;\n  platforms?: InputMaybe<Array<AppPlatform>>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport enum BuildIosEnterpriseProvisioning {\n  Adhoc = 'ADHOC',\n  Universal = 'UNIVERSAL'\n}\n\nexport enum BuildLimitThresholdExceededMetadataType {\n  Ios = 'IOS',\n  Total = 'TOTAL'\n}\n\nexport type BuildMetadataInput = {\n  appBuildVersion?: InputMaybe<Scalars['String']>;\n  appIdentifier?: InputMaybe<Scalars['String']>;\n  appName?: InputMaybe<Scalars['String']>;\n  appVersion?: InputMaybe<Scalars['String']>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  channel?: InputMaybe<Scalars['String']>;\n  cliVersion?: InputMaybe<Scalars['String']>;\n  credentialsSource?: InputMaybe<BuildCredentialsSource>;\n  customNodeVersion?: InputMaybe<Scalars['String']>;\n  customWorkflowName?: InputMaybe<Scalars['String']>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  distribution?: InputMaybe<DistributionType>;\n  environment?: InputMaybe<Scalars['String']>;\n  fingerprintHash?: InputMaybe<Scalars['String']>;\n  fingerprintSource?: InputMaybe<FingerprintSourceInput>;\n  gitCommitHash?: InputMaybe<Scalars['String']>;\n  gitCommitMessage?: InputMaybe<Scalars['String']>;\n  iosEnterpriseProvisioning?: InputMaybe<BuildIosEnterpriseProvisioning>;\n  isGitWorkingTreeDirty?: InputMaybe<Scalars['Boolean']>;\n  message?: InputMaybe<Scalars['String']>;\n  reactNativeVersion?: InputMaybe<Scalars['String']>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  requiredPackageManager?: InputMaybe<Scalars['String']>;\n  runFromCI?: InputMaybe<Scalars['Boolean']>;\n  runWithNoWaitFlag?: InputMaybe<Scalars['Boolean']>;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n  sdkVersion?: InputMaybe<Scalars['String']>;\n  selectedImage?: InputMaybe<Scalars['String']>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n  trackingContext?: InputMaybe<Scalars['JSONObject']>;\n  username?: InputMaybe<Scalars['String']>;\n  workflow?: InputMaybe<BuildWorkflow>;\n};\n\nexport enum BuildMode {\n  Build = 'BUILD',\n  Custom = 'CUSTOM',\n  Repack = 'REPACK',\n  Resign = 'RESIGN'\n}\n\nexport type BuildParamsInput = {\n  reactNativeVersion?: InputMaybe<Scalars['String']>;\n  resourceClass: BuildResourceClass;\n  sdkVersion?: InputMaybe<Scalars['String']>;\n};\n\nexport enum BuildPhase {\n  BuilderInfo = 'BUILDER_INFO',\n  CleanUpCredentials = 'CLEAN_UP_CREDENTIALS',\n  CompleteBuild = 'COMPLETE_BUILD',\n  ConfigureExpoUpdates = 'CONFIGURE_EXPO_UPDATES',\n  ConfigureXcodeProject = 'CONFIGURE_XCODE_PROJECT',\n  Custom = 'CUSTOM',\n  DownloadApplicationArchive = 'DOWNLOAD_APPLICATION_ARCHIVE',\n  EasBuildInternal = 'EAS_BUILD_INTERNAL',\n  FailBuild = 'FAIL_BUILD',\n  FixGradlew = 'FIX_GRADLEW',\n  InstallCustomTools = 'INSTALL_CUSTOM_TOOLS',\n  InstallDependencies = 'INSTALL_DEPENDENCIES',\n  InstallPods = 'INSTALL_PODS',\n  OnBuildCancelHook = 'ON_BUILD_CANCEL_HOOK',\n  OnBuildCompleteHook = 'ON_BUILD_COMPLETE_HOOK',\n  OnBuildErrorHook = 'ON_BUILD_ERROR_HOOK',\n  OnBuildSuccessHook = 'ON_BUILD_SUCCESS_HOOK',\n  ParseCustomWorkflowConfig = 'PARSE_CUSTOM_WORKFLOW_CONFIG',\n  PostInstallHook = 'POST_INSTALL_HOOK',\n  Prebuild = 'PREBUILD',\n  PrepareArtifacts = 'PREPARE_ARTIFACTS',\n  PrepareCredentials = 'PREPARE_CREDENTIALS',\n  PrepareProject = 'PREPARE_PROJECT',\n  PreInstallHook = 'PRE_INSTALL_HOOK',\n  PreUploadArtifactsHook = 'PRE_UPLOAD_ARTIFACTS_HOOK',\n  Queue = 'QUEUE',\n  ReadAppConfig = 'READ_APP_CONFIG',\n  ReadPackageJson = 'READ_PACKAGE_JSON',\n  RestoreCache = 'RESTORE_CACHE',\n  RunExpoDoctor = 'RUN_EXPO_DOCTOR',\n  RunFastlane = 'RUN_FASTLANE',\n  RunGradlew = 'RUN_GRADLEW',\n  SaveCache = 'SAVE_CACHE',\n  SetUpBuildEnvironment = 'SET_UP_BUILD_ENVIRONMENT',\n  SpinUpBuilder = 'SPIN_UP_BUILDER',\n  StartBuild = 'START_BUILD',\n  Unknown = 'UNKNOWN',\n  UploadApplicationArchive = 'UPLOAD_APPLICATION_ARCHIVE',\n  /** @deprecated No longer supported */\n  UploadArtifacts = 'UPLOAD_ARTIFACTS',\n  UploadBuildArtifacts = 'UPLOAD_BUILD_ARTIFACTS'\n}\n\nexport enum BuildPriority {\n  High = 'HIGH',\n  Normal = 'NORMAL',\n  NormalPlus = 'NORMAL_PLUS'\n}\n\nexport type BuildResignInput = {\n  applicationArchiveSource?: InputMaybe<ProjectArchiveSourceInput>;\n};\n\nexport enum BuildResourceClass {\n  AndroidDefault = 'ANDROID_DEFAULT',\n  AndroidLarge = 'ANDROID_LARGE',\n  AndroidMedium = 'ANDROID_MEDIUM',\n  IosDefault = 'IOS_DEFAULT',\n  /** @deprecated No longer available. Use IOS_M_LARGE instead. */\n  IosIntelLarge = 'IOS_INTEL_LARGE',\n  /** @deprecated No longer available. Use IOS_M_MEDIUM instead. */\n  IosIntelMedium = 'IOS_INTEL_MEDIUM',\n  IosLarge = 'IOS_LARGE',\n  /** @deprecated Use IOS_M_MEDIUM instead */\n  IosM1Large = 'IOS_M1_LARGE',\n  /** @deprecated Use IOS_M_MEDIUM instead */\n  IosM1Medium = 'IOS_M1_MEDIUM',\n  IosMedium = 'IOS_MEDIUM',\n  IosMLarge = 'IOS_M_LARGE',\n  IosMMedium = 'IOS_M_MEDIUM',\n  Legacy = 'LEGACY',\n  LinuxLarge = 'LINUX_LARGE',\n  LinuxMedium = 'LINUX_MEDIUM'\n}\n\nexport enum BuildRetryDisabledReason {\n  AlreadyRetried = 'ALREADY_RETRIED',\n  InvalidStatus = 'INVALID_STATUS',\n  IsGithubBuild = 'IS_GITHUB_BUILD',\n  NotCompletedYet = 'NOT_COMPLETED_YET',\n  TooMuchTimeElapsed = 'TOO_MUCH_TIME_ELAPSED'\n}\n\nexport enum BuildStatus {\n  Canceled = 'CANCELED',\n  Errored = 'ERRORED',\n  Finished = 'FINISHED',\n  InProgress = 'IN_PROGRESS',\n  InQueue = 'IN_QUEUE',\n  New = 'NEW',\n  PendingCancel = 'PENDING_CANCEL'\n}\n\nexport enum BuildTrigger {\n  EasCli = 'EAS_CLI',\n  GitBasedIntegration = 'GIT_BASED_INTEGRATION'\n}\n\nexport type BuildUpdatesInput = {\n  channel?: InputMaybe<Scalars['String']>;\n};\n\nexport enum BuildWorkflow {\n  Generic = 'GENERIC',\n  Managed = 'MANAGED',\n  Unknown = 'UNKNOWN'\n}\n\nexport type ChannelFilterInput = {\n  searchTerm?: InputMaybe<Scalars['String']>;\n};\n\nexport type CodeSigningInfoInput = {\n  alg: Scalars['String'];\n  keyid: Scalars['String'];\n  sig: Scalars['String'];\n};\n\nexport enum ContinentCode {\n  Af = 'AF',\n  An = 'AN',\n  As = 'AS',\n  Eu = 'EU',\n  Na = 'NA',\n  Oc = 'OC',\n  Sa = 'SA',\n  T1 = 'T1'\n}\n\nexport enum CrashSampleFor {\n  Newest = 'NEWEST',\n  Oldest = 'OLDEST'\n}\n\nexport type CrashesFilters = {\n  crashKind?: InputMaybe<Array<WorkerDeploymentCrashKind>>;\n  name?: InputMaybe<Array<Scalars['String']>>;\n};\n\nexport type CreateAccessTokenInput = {\n  actorID: Scalars['ID'];\n  note?: InputMaybe<Scalars['String']>;\n};\n\nexport type CreateAndroidSubmissionInput = {\n  appId: Scalars['ID'];\n  archiveSource?: InputMaybe<SubmissionArchiveSourceInput>;\n  archiveUrl?: InputMaybe<Scalars['String']>;\n  config: AndroidSubmissionConfigInput;\n  submittedBuildId?: InputMaybe<Scalars['ID']>;\n};\n\nexport type CreateEnvironmentSecretInput = {\n  name: Scalars['String'];\n  type?: InputMaybe<EnvironmentSecretType>;\n  value: Scalars['String'];\n};\n\nexport type CreateEnvironmentVariableInput = {\n  environments?: InputMaybe<Array<EnvironmentVariableEnvironment>>;\n  fileName?: InputMaybe<Scalars['String']>;\n  name: Scalars['String'];\n  overwrite?: InputMaybe<Scalars['Boolean']>;\n  type?: InputMaybe<EnvironmentSecretType>;\n  value: Scalars['String'];\n  visibility: EnvironmentVariableVisibility;\n};\n\nexport type CreateFingerprintInput = {\n  hash: Scalars['String'];\n  source?: InputMaybe<FingerprintSourceInput>;\n};\n\nexport type CreateGitHubAppInstallationInput = {\n  accountId: Scalars['ID'];\n  installationIdentifier: Scalars['Int'];\n};\n\nexport type CreateGitHubBuildTriggerInput = {\n  appId: Scalars['ID'];\n  autoSubmit: Scalars['Boolean'];\n  buildProfile: Scalars['String'];\n  environment?: InputMaybe<EnvironmentVariableEnvironment>;\n  executionBehavior: GitHubBuildTriggerExecutionBehavior;\n  isActive: Scalars['Boolean'];\n  platform: AppPlatform;\n  /** A branch or tag name, or a wildcard pattern where the code change originates from. For example, `main` or `release/*`. */\n  sourcePattern: Scalars['String'];\n  submitProfile?: InputMaybe<Scalars['String']>;\n  /** A branch name or a wildcard pattern that the pull request targets. For example, `main` or `release/*`. */\n  targetPattern?: InputMaybe<Scalars['String']>;\n  type: GitHubBuildTriggerType;\n};\n\nexport type CreateGitHubJobRunTriggerInput = {\n  appId: Scalars['ID'];\n  isActive: Scalars['Boolean'];\n  jobType: GitHubJobRunJobType;\n  sourcePattern: Scalars['String'];\n  targetPattern?: InputMaybe<Scalars['String']>;\n  triggerType: GitHubJobRunTriggerType;\n};\n\nexport type CreateGitHubRepositoryInput = {\n  appId: Scalars['ID'];\n  githubAppInstallationId: Scalars['ID'];\n  githubRepositoryIdentifier: Scalars['Int'];\n  nodeIdentifier: Scalars['String'];\n};\n\nexport type CreateGitHubRepositorySettingsInput = {\n  appId: Scalars['ID'];\n  /** The base directory is the directory to change to before starting a build. This string should be a properly formatted POSIX path starting with '/', './', or the name of the directory relative to the root of the repository. Valid examples include: '/apps/expo-app', './apps/expo-app', and 'apps/expo-app'. This is intended for monorepos or apps that live in a subdirectory of a repository. */\n  baseDirectory: Scalars['String'];\n};\n\nexport type CreateIosSubmissionInput = {\n  appId: Scalars['ID'];\n  archiveSource?: InputMaybe<SubmissionArchiveSourceInput>;\n  archiveUrl?: InputMaybe<Scalars['String']>;\n  config: IosSubmissionConfigInput;\n  submittedBuildId?: InputMaybe<Scalars['ID']>;\n};\n\nexport type CreateSharedEnvironmentVariableInput = {\n  environments?: InputMaybe<Array<EnvironmentVariableEnvironment>>;\n  fileName?: InputMaybe<Scalars['String']>;\n  isGlobal?: InputMaybe<Scalars['Boolean']>;\n  name: Scalars['String'];\n  overwrite?: InputMaybe<Scalars['Boolean']>;\n  type?: InputMaybe<EnvironmentSecretType>;\n  value: Scalars['String'];\n  visibility: EnvironmentVariableVisibility;\n};\n\nexport type CustomBuildConfigInput = {\n  path: Scalars['String'];\n};\n\nexport enum CustomDomainDnsRecordType {\n  A = 'A',\n  Cname = 'CNAME',\n  Txt = 'TXT'\n}\n\nexport enum CustomDomainStatus {\n  Active = 'ACTIVE',\n  Error = 'ERROR',\n  Pending = 'PENDING',\n  TimedOut = 'TIMED_OUT'\n}\n\nexport type DatasetTimespan = {\n  end: Scalars['DateTime'];\n  start: Scalars['DateTime'];\n};\n\nexport type DeploymentFilterInput = {\n  channel?: InputMaybe<Scalars['String']>;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n};\n\nexport enum DistributionType {\n  Internal = 'INTERNAL',\n  Simulator = 'SIMULATOR',\n  Store = 'STORE'\n}\n\nexport enum EasBuildBillingResourceClass {\n  Large = 'LARGE',\n  Medium = 'MEDIUM'\n}\n\nexport enum EasBuildDeprecationInfoType {\n  Internal = 'INTERNAL',\n  UserFacing = 'USER_FACING'\n}\n\nexport enum EasBuildWaiverType {\n  FastFailedBuild = 'FAST_FAILED_BUILD',\n  SystemError = 'SYSTEM_ERROR'\n}\n\nexport enum EasService {\n  Builds = 'BUILDS',\n  Jobs = 'JOBS',\n  Updates = 'UPDATES'\n}\n\nexport enum EasServiceMetric {\n  AssetsRequests = 'ASSETS_REQUESTS',\n  BandwidthUsage = 'BANDWIDTH_USAGE',\n  Builds = 'BUILDS',\n  ManifestRequests = 'MANIFEST_REQUESTS',\n  RunTime = 'RUN_TIME',\n  UniqueUpdaters = 'UNIQUE_UPDATERS',\n  UniqueUsers = 'UNIQUE_USERS'\n}\n\nexport enum EasTotalPlanEnablementUnit {\n  Build = 'BUILD',\n  Byte = 'BYTE',\n  Concurrency = 'CONCURRENCY',\n  Request = 'REQUEST',\n  Updater = 'UPDATER',\n  User = 'USER'\n}\n\nexport type EditUpdateBranchInput = {\n  appId?: InputMaybe<Scalars['ID']>;\n  id?: InputMaybe<Scalars['ID']>;\n  name?: InputMaybe<Scalars['String']>;\n  newName: Scalars['String'];\n};\n\nexport enum EntityTypeName {\n  AccountEntity = 'AccountEntity',\n  AccountSsoConfigurationEntity = 'AccountSSOConfigurationEntity',\n  AndroidAppCredentialsEntity = 'AndroidAppCredentialsEntity',\n  AndroidKeystoreEntity = 'AndroidKeystoreEntity',\n  AppEntity = 'AppEntity',\n  AppStoreConnectApiKeyEntity = 'AppStoreConnectApiKeyEntity',\n  AppleDeviceEntity = 'AppleDeviceEntity',\n  AppleDistributionCertificateEntity = 'AppleDistributionCertificateEntity',\n  AppleProvisioningProfileEntity = 'AppleProvisioningProfileEntity',\n  AppleTeamEntity = 'AppleTeamEntity',\n  BranchEntity = 'BranchEntity',\n  ChannelEntity = 'ChannelEntity',\n  CustomerEntity = 'CustomerEntity',\n  GoogleServiceAccountKeyEntity = 'GoogleServiceAccountKeyEntity',\n  IosAppCredentialsEntity = 'IosAppCredentialsEntity',\n  UserInvitationEntity = 'UserInvitationEntity',\n  UserPermissionEntity = 'UserPermissionEntity',\n  WorkerCustomDomainEntity = 'WorkerCustomDomainEntity',\n  WorkerDeploymentAliasEntity = 'WorkerDeploymentAliasEntity',\n  WorkerEntity = 'WorkerEntity',\n  WorkflowEntity = 'WorkflowEntity',\n  WorkflowRevisionEntity = 'WorkflowRevisionEntity'\n}\n\nexport enum EnvironmentSecretType {\n  FileBase64 = 'FILE_BASE64',\n  String = 'STRING'\n}\n\nexport enum EnvironmentVariableEnvironment {\n  Development = 'DEVELOPMENT',\n  Preview = 'PREVIEW',\n  Production = 'PRODUCTION'\n}\n\nexport enum EnvironmentVariableScope {\n  Project = 'PROJECT',\n  Shared = 'SHARED'\n}\n\nexport enum EnvironmentVariableVisibility {\n  Public = 'PUBLIC',\n  Secret = 'SECRET',\n  Sensitive = 'SENSITIVE'\n}\n\nexport enum Experiment {\n  Orbit = 'ORBIT'\n}\n\nexport enum Feature {\n  /** Priority Builds */\n  Builds = 'BUILDS',\n  /** Funds support for open source development */\n  OpenSource = 'OPEN_SOURCE',\n  /** Top Tier Support */\n  Support = 'SUPPORT',\n  /** Share access to projects */\n  Teams = 'TEAMS'\n}\n\nexport type FingerprintBuildsFilterInput = {\n  channel?: InputMaybe<Scalars['String']>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  distributions?: InputMaybe<Array<DistributionType>>;\n  platforms?: InputMaybe<Array<AppPlatform>>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport type FingerprintFilterInput = {\n  hashes?: InputMaybe<Array<Scalars['String']>>;\n};\n\nexport type FingerprintInfo = {\n  fingerprintHash: Scalars['String'];\n  fingerprintSource: FingerprintSourceInput;\n};\n\nexport type FingerprintInfoGroup = {\n  android?: InputMaybe<FingerprintInfo>;\n  ios?: InputMaybe<FingerprintInfo>;\n  web?: InputMaybe<FingerprintInfo>;\n};\n\nexport type FingerprintSourceInput = {\n  bucketKey?: InputMaybe<Scalars['String']>;\n  isDebugFingerprint?: InputMaybe<Scalars['Boolean']>;\n  type?: InputMaybe<FingerprintSourceType>;\n};\n\nexport enum FingerprintSourceType {\n  Gcs = 'GCS'\n}\n\nexport type GenerateLogRocketOrganizationLinkingUrlInput = {\n  accountId: Scalars['ID'];\n  callbackUrl: Scalars['String'];\n};\n\nexport enum GitHubAppEnvironment {\n  Development = 'DEVELOPMENT',\n  Production = 'PRODUCTION',\n  Staging = 'STAGING'\n}\n\nexport enum GitHubAppInstallationStatus {\n  Active = 'ACTIVE',\n  NotInstalled = 'NOT_INSTALLED',\n  Suspended = 'SUSPENDED'\n}\n\nexport type GitHubBuildInput = {\n  appId: Scalars['ID'];\n  autoSubmit?: InputMaybe<Scalars['Boolean']>;\n  baseDirectory?: InputMaybe<Scalars['String']>;\n  buildProfile: Scalars['String'];\n  environment?: InputMaybe<EnvironmentVariableEnvironment>;\n  gitRef: Scalars['String'];\n  platform: AppPlatform;\n  /** Repack the golden dev client build instead of running full build process. Used for onboarding. Do not use outside of onboarding flow, as for now it's only created with this specific use case in mind. */\n  repack?: InputMaybe<Scalars['Boolean']>;\n  submitProfile?: InputMaybe<Scalars['String']>;\n};\n\nexport enum GitHubBuildTriggerExecutionBehavior {\n  Always = 'ALWAYS',\n  BaseDirectoryChanged = 'BASE_DIRECTORY_CHANGED'\n}\n\nexport enum GitHubBuildTriggerRunStatus {\n  Errored = 'ERRORED',\n  Success = 'SUCCESS'\n}\n\nexport enum GitHubBuildTriggerType {\n  PullRequestUpdated = 'PULL_REQUEST_UPDATED',\n  PushToBranch = 'PUSH_TO_BRANCH',\n  TagUpdated = 'TAG_UPDATED'\n}\n\nexport enum GitHubJobRunJobType {\n  PublishUpdate = 'PUBLISH_UPDATE'\n}\n\nexport enum GitHubJobRunTriggerRunStatus {\n  Errored = 'ERRORED',\n  Success = 'SUCCESS'\n}\n\nexport enum GitHubJobRunTriggerType {\n  PullRequestUpdated = 'PULL_REQUEST_UPDATED',\n  PushToBranch = 'PUSH_TO_BRANCH'\n}\n\nexport type GoogleServiceAccountKeyInput = {\n  jsonKey: Scalars['JSONObject'];\n};\n\n/**\n * The value field is always sent from the client as a string,\n * and then it's parsed server-side according to the filterType\n */\nexport type InsightsFilter = {\n  filterType: InsightsFilterType;\n  value: Scalars['String'];\n};\n\nexport enum InsightsFilterType {\n  Platform = 'PLATFORM'\n}\n\nexport type InsightsTimespan = {\n  end: Scalars['DateTime'];\n  start: Scalars['DateTime'];\n};\n\nexport enum InvoiceDiscountType {\n  Amount = 'AMOUNT',\n  Percentage = 'PERCENTAGE'\n}\n\nexport type IosAppBuildCredentialsFilter = {\n  iosDistributionType?: InputMaybe<IosDistributionType>;\n};\n\nexport type IosAppBuildCredentialsInput = {\n  distributionCertificateId: Scalars['ID'];\n  iosDistributionType: IosDistributionType;\n  provisioningProfileId: Scalars['ID'];\n};\n\nexport type IosAppCredentialsFilter = {\n  appleAppIdentifierId?: InputMaybe<Scalars['String']>;\n};\n\nexport type IosAppCredentialsInput = {\n  appStoreConnectApiKeyForBuildsId?: InputMaybe<Scalars['ID']>;\n  appStoreConnectApiKeyForSubmissionsId?: InputMaybe<Scalars['ID']>;\n  appleTeamId?: InputMaybe<Scalars['ID']>;\n  pushKeyId?: InputMaybe<Scalars['ID']>;\n};\n\n/** @deprecated Use developmentClient option instead. */\nexport enum IosBuildType {\n  DevelopmentClient = 'DEVELOPMENT_CLIENT',\n  Release = 'RELEASE'\n}\n\nexport type IosBuilderEnvironmentInput = {\n  bun?: InputMaybe<Scalars['String']>;\n  bundler?: InputMaybe<Scalars['String']>;\n  cocoapods?: InputMaybe<Scalars['String']>;\n  env?: InputMaybe<Scalars['JSONObject']>;\n  expoCli?: InputMaybe<Scalars['String']>;\n  fastlane?: InputMaybe<Scalars['String']>;\n  image?: InputMaybe<Scalars['String']>;\n  node?: InputMaybe<Scalars['String']>;\n  pnpm?: InputMaybe<Scalars['String']>;\n  yarn?: InputMaybe<Scalars['String']>;\n};\n\nexport enum IosDistributionType {\n  AdHoc = 'AD_HOC',\n  AppStore = 'APP_STORE',\n  Development = 'DEVELOPMENT',\n  Enterprise = 'ENTERPRISE'\n}\n\nexport type IosJobDistributionCertificateInput = {\n  dataBase64: Scalars['String'];\n  password: Scalars['String'];\n};\n\nexport type IosJobInput = {\n  applicationArchivePath?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  artifactPath?: InputMaybe<Scalars['String']>;\n  buildArtifactPaths?: InputMaybe<Array<Scalars['String']>>;\n  buildConfiguration?: InputMaybe<Scalars['String']>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  buildType?: InputMaybe<IosBuildType>;\n  builderEnvironment?: InputMaybe<IosBuilderEnvironmentInput>;\n  cache?: InputMaybe<BuildCacheInput>;\n  customBuildConfig?: InputMaybe<CustomBuildConfigInput>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  /** @deprecated */\n  distribution?: InputMaybe<DistributionType>;\n  experimental?: InputMaybe<Scalars['JSONObject']>;\n  loggerLevel?: InputMaybe<WorkerLoggerLevel>;\n  mode?: InputMaybe<BuildMode>;\n  projectArchive: ProjectArchiveSourceInput;\n  projectRootDirectory: Scalars['String'];\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  scheme?: InputMaybe<Scalars['String']>;\n  secrets?: InputMaybe<IosJobSecretsInput>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n  triggeredBy?: InputMaybe<BuildTrigger>;\n  type: BuildWorkflow;\n  updates?: InputMaybe<BuildUpdatesInput>;\n  username?: InputMaybe<Scalars['String']>;\n  version?: InputMaybe<IosJobVersionInput>;\n};\n\nexport type IosJobOverridesInput = {\n  applicationArchivePath?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  artifactPath?: InputMaybe<Scalars['String']>;\n  buildArtifactPaths?: InputMaybe<Array<Scalars['String']>>;\n  buildConfiguration?: InputMaybe<Scalars['String']>;\n  buildProfile?: InputMaybe<Scalars['String']>;\n  /** @deprecated */\n  buildType?: InputMaybe<IosBuildType>;\n  builderEnvironment?: InputMaybe<IosBuilderEnvironmentInput>;\n  cache?: InputMaybe<BuildCacheInput>;\n  customBuildConfig?: InputMaybe<CustomBuildConfigInput>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  /** @deprecated */\n  distribution?: InputMaybe<DistributionType>;\n  experimental?: InputMaybe<Scalars['JSONObject']>;\n  loggerLevel?: InputMaybe<WorkerLoggerLevel>;\n  mode?: InputMaybe<BuildMode>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  resign?: InputMaybe<BuildResignInput>;\n  scheme?: InputMaybe<Scalars['String']>;\n  secrets?: InputMaybe<IosJobSecretsInput>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n  type?: InputMaybe<BuildWorkflow>;\n  updates?: InputMaybe<BuildUpdatesInput>;\n  username?: InputMaybe<Scalars['String']>;\n  version?: InputMaybe<IosJobVersionInput>;\n};\n\nexport type IosJobSecretsInput = {\n  buildCredentials?: InputMaybe<Array<InputMaybe<IosJobTargetCredentialsInput>>>;\n  robotAccessToken?: InputMaybe<Scalars['String']>;\n};\n\nexport type IosJobTargetCredentialsInput = {\n  distributionCertificate: IosJobDistributionCertificateInput;\n  provisioningProfileBase64: Scalars['String'];\n  targetName: Scalars['String'];\n};\n\nexport type IosJobVersionInput = {\n  buildNumber: Scalars['String'];\n};\n\n/** @deprecated Use developmentClient option instead. */\nexport enum IosManagedBuildType {\n  DevelopmentClient = 'DEVELOPMENT_CLIENT',\n  Release = 'RELEASE'\n}\n\nexport enum IosSchemeBuildConfiguration {\n  Debug = 'DEBUG',\n  Release = 'RELEASE'\n}\n\nexport type IosSubmissionConfigInput = {\n  appleAppSpecificPassword?: InputMaybe<Scalars['String']>;\n  appleIdUsername?: InputMaybe<Scalars['String']>;\n  archiveUrl?: InputMaybe<Scalars['String']>;\n  ascApiKey?: InputMaybe<AscApiKeyInput>;\n  ascApiKeyId?: InputMaybe<Scalars['String']>;\n  ascAppIdentifier: Scalars['String'];\n  isVerboseFastlaneEnabled?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport enum JobRunPriority {\n  High = 'HIGH',\n  Normal = 'NORMAL'\n}\n\nexport enum JobRunStatus {\n  Canceled = 'CANCELED',\n  Errored = 'ERRORED',\n  Finished = 'FINISHED',\n  InProgress = 'IN_PROGRESS',\n  InQueue = 'IN_QUEUE',\n  New = 'NEW',\n  PendingCancel = 'PENDING_CANCEL'\n}\n\nexport type LinkLogRocketOrganizationToExpoAccountInput = {\n  accountId: Scalars['ID'];\n  client_id: Scalars['String'];\n  client_secret: Scalars['String'];\n  orgName: Scalars['String'];\n  orgSlug: Scalars['String'];\n  state: Scalars['String'];\n};\n\nexport type LinkSharedEnvironmentVariableInput = {\n  appId: Scalars['ID'];\n  environment?: InputMaybe<EnvironmentVariableEnvironment>;\n  environmentVariableId: Scalars['ID'];\n};\n\nexport type LogsTimespan = {\n  end: Scalars['DateTime'];\n  start?: InputMaybe<Scalars['DateTime']>;\n};\n\nexport enum MailchimpAudience {\n  ExpoDevelopers = 'EXPO_DEVELOPERS',\n  ExpoDeveloperOnboarding = 'EXPO_DEVELOPER_ONBOARDING',\n  LaunchParty_2024 = 'LAUNCH_PARTY_2024',\n  NonprodExpoDevelopers = 'NONPROD_EXPO_DEVELOPERS'\n}\n\nexport enum MailchimpTag {\n  DevClientUsers = 'DEV_CLIENT_USERS',\n  DidSubscribeToEasAtLeastOnce = 'DID_SUBSCRIBE_TO_EAS_AT_LEAST_ONCE',\n  EasMasterList = 'EAS_MASTER_LIST',\n  NewsletterSignupList = 'NEWSLETTER_SIGNUP_LIST'\n}\n\nexport enum NotificationEvent {\n  BuildComplete = 'BUILD_COMPLETE',\n  BuildErrored = 'BUILD_ERRORED',\n  BuildLimitThresholdExceeded = 'BUILD_LIMIT_THRESHOLD_EXCEEDED',\n  BuildPlanCreditThresholdExceeded = 'BUILD_PLAN_CREDIT_THRESHOLD_EXCEEDED',\n  SubmissionComplete = 'SUBMISSION_COMPLETE',\n  SubmissionErrored = 'SUBMISSION_ERRORED',\n  Test = 'TEST'\n}\n\nexport type NotificationSubscriptionFilter = {\n  accountId?: InputMaybe<Scalars['ID']>;\n  appId?: InputMaybe<Scalars['ID']>;\n  event?: InputMaybe<NotificationEvent>;\n  type?: InputMaybe<NotificationType>;\n};\n\nexport enum NotificationType {\n  Email = 'EMAIL',\n  Web = 'WEB'\n}\n\nexport enum OfferType {\n  /** Addon, or supplementary subscription */\n  Addon = 'ADDON',\n  /** Advanced Purchase of Paid Resource */\n  Prepaid = 'PREPAID',\n  /** Term subscription */\n  Subscription = 'SUBSCRIPTION'\n}\n\nexport enum OnboardingDeviceType {\n  Device = 'DEVICE',\n  Simulator = 'SIMULATOR'\n}\n\nexport enum OnboardingEnvironment {\n  DevBuild = 'DEV_BUILD',\n  ExpoGo = 'EXPO_GO'\n}\n\nexport enum Order {\n  Asc = 'ASC',\n  Desc = 'DESC'\n}\n\nexport type PartialManifest = {\n  assets: Array<InputMaybe<PartialManifestAsset>>;\n  extra?: InputMaybe<Scalars['JSONObject']>;\n  launchAsset: PartialManifestAsset;\n};\n\nexport type PartialManifestAsset = {\n  bundleKey: Scalars['String'];\n  contentType: Scalars['String'];\n  fileExtension?: InputMaybe<Scalars['String']>;\n  fileSHA256: Scalars['String'];\n  storageKey: Scalars['String'];\n};\n\nexport enum Permission {\n  Admin = 'ADMIN',\n  Own = 'OWN',\n  Publish = 'PUBLISH',\n  View = 'VIEW'\n}\n\nexport type ProjectArchiveSourceInput = {\n  bucketKey?: InputMaybe<Scalars['String']>;\n  gitRef?: InputMaybe<Scalars['String']>;\n  metadataLocation?: InputMaybe<Scalars['String']>;\n  repositoryUrl?: InputMaybe<Scalars['String']>;\n  type: ProjectArchiveSourceType;\n  url?: InputMaybe<Scalars['String']>;\n};\n\nexport enum ProjectArchiveSourceType {\n  Gcs = 'GCS',\n  Git = 'GIT',\n  None = 'NONE',\n  S3 = 'S3',\n  Url = 'URL'\n}\n\nexport type PublishUpdateGroupInput = {\n  awaitingCodeSigningInfo?: InputMaybe<Scalars['Boolean']>;\n  branchId: Scalars['String'];\n  environment?: InputMaybe<EnvironmentVariableEnvironment>;\n  excludedAssets?: InputMaybe<Array<PartialManifestAsset>>;\n  fingerprintInfoGroup?: InputMaybe<FingerprintInfoGroup>;\n  gitCommitHash?: InputMaybe<Scalars['String']>;\n  isGitWorkingTreeDirty?: InputMaybe<Scalars['Boolean']>;\n  message?: InputMaybe<Scalars['String']>;\n  rollBackToEmbeddedInfoGroup?: InputMaybe<UpdateRollBackToEmbeddedGroup>;\n  rolloutInfoGroup?: InputMaybe<UpdateRolloutInfoGroup>;\n  runtimeVersion: Scalars['String'];\n  turtleJobRunId?: InputMaybe<Scalars['String']>;\n  updateInfoGroup?: InputMaybe<UpdateInfoGroup>;\n};\n\nexport enum RequestMethod {\n  Delete = 'DELETE',\n  Get = 'GET',\n  Head = 'HEAD',\n  Options = 'OPTIONS',\n  Patch = 'PATCH',\n  Post = 'POST',\n  Put = 'PUT'\n}\n\nexport type RequestsFilters = {\n  cacheStatus?: InputMaybe<Array<ResponseCacheStatus>>;\n  continent?: InputMaybe<Array<ContinentCode>>;\n  hasCustomDomainOrigin?: InputMaybe<Scalars['Boolean']>;\n  isAsset?: InputMaybe<Scalars['Boolean']>;\n  isCrash?: InputMaybe<Scalars['Boolean']>;\n  isLimitExceeded?: InputMaybe<Scalars['Boolean']>;\n  isVerifiedBot?: InputMaybe<Scalars['Boolean']>;\n  method?: InputMaybe<Array<RequestMethod>>;\n  os?: InputMaybe<Array<UserAgentOs>>;\n  pathname?: InputMaybe<Scalars['String']>;\n  requestId?: InputMaybe<Array<Scalars['WorkerDeploymentRequestID']>>;\n  responseType?: InputMaybe<Array<ResponseType>>;\n  status?: InputMaybe<Array<Scalars['Int']>>;\n  statusType?: InputMaybe<Array<ResponseStatusType>>;\n};\n\nexport type RequestsOrderBy = {\n  direction?: InputMaybe<RequestsOrderByDirection>;\n  field: RequestsOrderByField;\n};\n\nexport enum RequestsOrderByDirection {\n  Asc = 'ASC',\n  Desc = 'DESC'\n}\n\nexport enum RequestsOrderByField {\n  AssetsSum = 'ASSETS_SUM',\n  CacheHitRatio = 'CACHE_HIT_RATIO',\n  CachePassRatio = 'CACHE_PASS_RATIO',\n  CrashesSum = 'CRASHES_SUM',\n  Duration = 'DURATION',\n  RequestsSum = 'REQUESTS_SUM'\n}\n\nexport enum ResourceClassExperiment {\n  C3D = 'C3D',\n  N2 = 'N2'\n}\n\nexport enum ResponseCacheStatus {\n  Hit = 'HIT',\n  Miss = 'MISS',\n  Pass = 'PASS'\n}\n\nexport enum ResponseStatusType {\n  ClientError = 'CLIENT_ERROR',\n  None = 'NONE',\n  Redirect = 'REDIRECT',\n  ServerError = 'SERVER_ERROR',\n  Successful = 'SUCCESSFUL'\n}\n\nexport enum ResponseType {\n  Asset = 'ASSET',\n  Crash = 'CRASH',\n  Rejected = 'REJECTED',\n  Route = 'ROUTE'\n}\n\nexport type RobotDataInput = {\n  name?: InputMaybe<Scalars['String']>;\n};\n\nexport enum Role {\n  Admin = 'ADMIN',\n  Custom = 'CUSTOM',\n  Developer = 'DEVELOPER',\n  HasAdmin = 'HAS_ADMIN',\n  NotAdmin = 'NOT_ADMIN',\n  Owner = 'OWNER',\n  ViewOnly = 'VIEW_ONLY'\n}\n\nexport type RuntimeBuildsFilterInput = {\n  channel?: InputMaybe<Scalars['String']>;\n  developmentClient?: InputMaybe<Scalars['Boolean']>;\n  distributions?: InputMaybe<Array<DistributionType>>;\n  platforms?: InputMaybe<Array<AppPlatform>>;\n  releaseChannel?: InputMaybe<Scalars['String']>;\n  simulator?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport type RuntimeDeploymentsFilterInput = {\n  channel?: InputMaybe<Scalars['String']>;\n};\n\nexport type RuntimeFilterInput = {\n  /** Only return runtimes shared with this branch */\n  branchId?: InputMaybe<Scalars['String']>;\n};\n\nexport type SsoUserDataInput = {\n  firstName?: InputMaybe<Scalars['String']>;\n  lastName?: InputMaybe<Scalars['String']>;\n};\n\nexport type SecondFactorDeviceConfiguration = {\n  isPrimary: Scalars['Boolean'];\n  method: SecondFactorMethod;\n  name: Scalars['String'];\n  smsPhoneNumber?: InputMaybe<Scalars['String']>;\n};\n\nexport enum SecondFactorMethod {\n  /** Google Authenticator (TOTP) */\n  Authenticator = 'AUTHENTICATOR',\n  /** SMS */\n  Sms = 'SMS'\n}\n\nexport enum StandardOffer {\n  /** $29 USD per month, 30 day trial */\n  Default = 'DEFAULT',\n  /** $800 USD per month */\n  Support = 'SUPPORT',\n  /** $29 USD per month, 1 year trial */\n  YcDeals = 'YC_DEALS',\n  /** $348 USD per year, 30 day trial */\n  YearlySub = 'YEARLY_SUB'\n}\n\n/** Possible Incident impact values from Expo status page API. */\nexport enum StatuspageIncidentImpact {\n  Critical = 'CRITICAL',\n  Maintenance = 'MAINTENANCE',\n  Major = 'MAJOR',\n  Minor = 'MINOR',\n  None = 'NONE'\n}\n\n/** Possible Incident statuses from Expo status page API. */\nexport enum StatuspageIncidentStatus {\n  Completed = 'COMPLETED',\n  Identified = 'IDENTIFIED',\n  Investigating = 'INVESTIGATING',\n  InProgress = 'IN_PROGRESS',\n  Monitoring = 'MONITORING',\n  Resolved = 'RESOLVED',\n  Scheduled = 'SCHEDULED',\n  Verifying = 'VERIFYING'\n}\n\n/** Name of a service monitored by Expo status page. */\nexport enum StatuspageServiceName {\n  EasBuild = 'EAS_BUILD',\n  EasSubmit = 'EAS_SUBMIT',\n  EasUpdate = 'EAS_UPDATE',\n  GithubApiRequests = 'GITHUB_API_REQUESTS',\n  GithubWebhooks = 'GITHUB_WEBHOOKS'\n}\n\n/** Possible statuses for a service. */\nexport enum StatuspageServiceStatus {\n  DegradedPerformance = 'DEGRADED_PERFORMANCE',\n  MajorOutage = 'MAJOR_OUTAGE',\n  Operational = 'OPERATIONAL',\n  PartialOutage = 'PARTIAL_OUTAGE',\n  UnderMaintenance = 'UNDER_MAINTENANCE'\n}\n\nexport enum SubmissionAndroidArchiveType {\n  Aab = 'AAB',\n  Apk = 'APK'\n}\n\nexport enum SubmissionAndroidReleaseStatus {\n  Completed = 'COMPLETED',\n  Draft = 'DRAFT',\n  Halted = 'HALTED',\n  InProgress = 'IN_PROGRESS'\n}\n\nexport enum SubmissionAndroidTrack {\n  Alpha = 'ALPHA',\n  Beta = 'BETA',\n  Internal = 'INTERNAL',\n  Production = 'PRODUCTION'\n}\n\nexport type SubmissionArchiveSourceInput = {\n  /** Required if the archive source type is GCS_BUILD_APPLICATION_ARCHIVE, GCS_BUILD_APPLICATION_ARCHIVE_ORCHESTRATOR or GCS_SUBMIT_ARCHIVE */\n  bucketKey?: InputMaybe<Scalars['String']>;\n  type: SubmissionArchiveSourceType;\n  /** Required if the archive source type is URL */\n  url?: InputMaybe<Scalars['String']>;\n};\n\nexport enum SubmissionArchiveSourceType {\n  GcsBuildApplicationArchive = 'GCS_BUILD_APPLICATION_ARCHIVE',\n  GcsBuildApplicationArchiveOrchestrator = 'GCS_BUILD_APPLICATION_ARCHIVE_ORCHESTRATOR',\n  GcsSubmitArchive = 'GCS_SUBMIT_ARCHIVE',\n  Url = 'URL'\n}\n\nexport type SubmissionFilter = {\n  platform?: InputMaybe<AppPlatform>;\n  status?: InputMaybe<SubmissionStatus>;\n};\n\nexport enum SubmissionPriority {\n  High = 'HIGH',\n  Normal = 'NORMAL'\n}\n\nexport enum SubmissionStatus {\n  AwaitingBuild = 'AWAITING_BUILD',\n  Canceled = 'CANCELED',\n  Errored = 'ERRORED',\n  Finished = 'FINISHED',\n  InProgress = 'IN_PROGRESS',\n  InQueue = 'IN_QUEUE'\n}\n\nexport enum TargetEntityMutationType {\n  Create = 'CREATE',\n  Delete = 'DELETE',\n  Update = 'UPDATE'\n}\n\nexport type TimelineActivityFilterInput = {\n  channels?: InputMaybe<Array<Scalars['String']>>;\n  platforms?: InputMaybe<Array<AppPlatform>>;\n  releaseChannels?: InputMaybe<Array<Scalars['String']>>;\n  types?: InputMaybe<Array<ActivityTimelineProjectActivityType>>;\n};\n\nexport type UpdateEnvironmentVariableInput = {\n  environments?: InputMaybe<Array<EnvironmentVariableEnvironment>>;\n  fileName?: InputMaybe<Scalars['String']>;\n  id: Scalars['ID'];\n  isGlobal?: InputMaybe<Scalars['Boolean']>;\n  name?: InputMaybe<Scalars['String']>;\n  type?: InputMaybe<EnvironmentSecretType>;\n  value?: InputMaybe<Scalars['String']>;\n  visibility?: InputMaybe<EnvironmentVariableVisibility>;\n};\n\nexport type UpdateFilterInput = {\n  fingerprintHash?: InputMaybe<Scalars['String']>;\n  hasFingerprint?: InputMaybe<Scalars['Boolean']>;\n  runtimeVersion?: InputMaybe<Scalars['String']>;\n};\n\nexport type UpdateGitHubBuildTriggerInput = {\n  autoSubmit: Scalars['Boolean'];\n  buildProfile: Scalars['String'];\n  environment?: InputMaybe<EnvironmentVariableEnvironment>;\n  executionBehavior: GitHubBuildTriggerExecutionBehavior;\n  isActive: Scalars['Boolean'];\n  platform: AppPlatform;\n  sourcePattern: Scalars['String'];\n  submitProfile?: InputMaybe<Scalars['String']>;\n  targetPattern?: InputMaybe<Scalars['String']>;\n  type: GitHubBuildTriggerType;\n};\n\nexport type UpdateGitHubJobRunTriggerInput = {\n  isActive: Scalars['Boolean'];\n  sourcePattern: Scalars['String'];\n  targetPattern?: InputMaybe<Scalars['String']>;\n};\n\nexport type UpdateGitHubRepositorySettingsInput = {\n  baseDirectory: Scalars['String'];\n};\n\nexport type UpdateInfoGroup = {\n  android?: InputMaybe<PartialManifest>;\n  ios?: InputMaybe<PartialManifest>;\n  web?: InputMaybe<PartialManifest>;\n};\n\nexport type UpdateRollBackToEmbeddedGroup = {\n  android?: InputMaybe<Scalars['Boolean']>;\n  ios?: InputMaybe<Scalars['Boolean']>;\n  web?: InputMaybe<Scalars['Boolean']>;\n};\n\nexport type UpdateRolloutInfo = {\n  rolloutControlUpdateId: Scalars['ID'];\n  rolloutPercentage: Scalars['Int'];\n};\n\nexport type UpdateRolloutInfoGroup = {\n  android?: InputMaybe<UpdateRolloutInfo>;\n  ios?: InputMaybe<UpdateRolloutInfo>;\n  web?: InputMaybe<UpdateRolloutInfo>;\n};\n\nexport type UpdatesFilter = {\n  platform?: InputMaybe<AppPlatform>;\n  runtimeVersions?: InputMaybe<Array<Scalars['String']>>;\n  sdkVersions?: InputMaybe<Array<Scalars['String']>>;\n};\n\nexport enum UploadSessionType {\n  EasBuildGcsProjectMetadata = 'EAS_BUILD_GCS_PROJECT_METADATA',\n  EasBuildGcsProjectSources = 'EAS_BUILD_GCS_PROJECT_SOURCES',\n  /** @deprecated Use EAS_BUILD_GCS_PROJECT_SOURCES instead. */\n  EasBuildProjectSources = 'EAS_BUILD_PROJECT_SOURCES',\n  /** @deprecated Use EAS_SUBMIT_GCS_APP_ARCHIVE instead. */\n  EasSubmitAppArchive = 'EAS_SUBMIT_APP_ARCHIVE',\n  EasSubmitGcsAppArchive = 'EAS_SUBMIT_GCS_APP_ARCHIVE',\n  EasUpdateFingerprint = 'EAS_UPDATE_FINGERPRINT'\n}\n\nexport enum UsageMetricType {\n  Bandwidth = 'BANDWIDTH',\n  Build = 'BUILD',\n  Minute = 'MINUTE',\n  Request = 'REQUEST',\n  Update = 'UPDATE',\n  User = 'USER'\n}\n\nexport enum UsageMetricsGranularity {\n  Day = 'DAY',\n  Hour = 'HOUR',\n  Minute = 'MINUTE',\n  Total = 'TOTAL'\n}\n\nexport type UsageMetricsTimespan = {\n  end: Scalars['DateTime'];\n  start: Scalars['DateTime'];\n};\n\nexport enum UserAgentBrowser {\n  AndroidMobile = 'ANDROID_MOBILE',\n  Chrome = 'CHROME',\n  ChromeIos = 'CHROME_IOS',\n  Edge = 'EDGE',\n  FacebookMobile = 'FACEBOOK_MOBILE',\n  Firefox = 'FIREFOX',\n  FirefoxIos = 'FIREFOX_IOS',\n  InternetExplorer = 'INTERNET_EXPLORER',\n  Konqueror = 'KONQUEROR',\n  Mozilla = 'MOZILLA',\n  Opera = 'OPERA',\n  Safari = 'SAFARI',\n  SafariMobile = 'SAFARI_MOBILE',\n  SamsungInternet = 'SAMSUNG_INTERNET',\n  UcBrowser = 'UC_BROWSER'\n}\n\nexport enum UserAgentOs {\n  Android = 'ANDROID',\n  ChromeOs = 'CHROME_OS',\n  Ios = 'IOS',\n  IpadOs = 'IPAD_OS',\n  Linux = 'LINUX',\n  MacOs = 'MAC_OS',\n  Windows = 'WINDOWS'\n}\n\nexport type UserAuditLogExportInput = {\n  createdAfter: Scalars['String'];\n  createdBefore: Scalars['String'];\n  format: AuditLogsExportFormat;\n  targetEntityMutationType?: InputMaybe<Array<TargetEntityMutationType>>;\n  targetEntityTypeName?: InputMaybe<Array<UserEntityTypeName>>;\n  userId: Scalars['ID'];\n};\n\nexport type UserAuditLogFilterInput = {\n  entityTypes?: InputMaybe<Array<UserEntityTypeName>>;\n  mutationTypes?: InputMaybe<Array<TargetEntityMutationType>>;\n};\n\nexport type UserDataInput = {\n  email?: InputMaybe<Scalars['String']>;\n  firstName?: InputMaybe<Scalars['String']>;\n  fullName?: InputMaybe<Scalars['String']>;\n  id?: InputMaybe<Scalars['ID']>;\n  lastName?: InputMaybe<Scalars['String']>;\n  profilePhoto?: InputMaybe<Scalars['String']>;\n  username?: InputMaybe<Scalars['String']>;\n};\n\nexport enum UserEntityTypeName {\n  AccessTokenEntity = 'AccessTokenEntity',\n  DiscordUserEntity = 'DiscordUserEntity',\n  GitHubUserEntity = 'GitHubUserEntity',\n  PasswordEntity = 'PasswordEntity',\n  SsoUserEntity = 'SSOUserEntity',\n  UserEntity = 'UserEntity',\n  UserPermissionEntity = 'UserPermissionEntity',\n  UserSecondFactorBackupCodesEntity = 'UserSecondFactorBackupCodesEntity',\n  UserSecondFactorDeviceEntity = 'UserSecondFactorDeviceEntity'\n}\n\nexport type UserPreferencesInput = {\n  onboarding?: InputMaybe<UserPreferencesOnboardingInput>;\n  selectedAccountName?: InputMaybe<Scalars['String']>;\n};\n\nexport type UserPreferencesOnboardingInput = {\n  appId: Scalars['ID'];\n  deviceType?: InputMaybe<OnboardingDeviceType>;\n  environment?: InputMaybe<OnboardingEnvironment>;\n  isCLIDone?: InputMaybe<Scalars['Boolean']>;\n  lastUsed: Scalars['String'];\n  platform?: InputMaybe<AppPlatform>;\n};\n\nexport type WebNotificationUpdateReadStateInput = {\n  id: Scalars['ID'];\n  isRead: Scalars['Boolean'];\n};\n\nexport type WebhookFilter = {\n  event?: InputMaybe<WebhookType>;\n};\n\nexport type WebhookInput = {\n  event: WebhookType;\n  secret: Scalars['String'];\n  url: Scalars['String'];\n};\n\nexport enum WebhookType {\n  Build = 'BUILD',\n  Submit = 'SUBMIT'\n}\n\nexport enum WorkerDeploymentCrashKind {\n  ExceededCpu = 'EXCEEDED_CPU',\n  ExceededMemory = 'EXCEEDED_MEMORY',\n  ExceededSubrequests = 'EXCEEDED_SUBREQUESTS',\n  Generic = 'GENERIC',\n  Internal = 'INTERNAL',\n  ResponseStreamDisconnected = 'RESPONSE_STREAM_DISCONNECTED'\n}\n\nexport enum WorkerDeploymentLogLevel {\n  Debug = 'DEBUG',\n  Error = 'ERROR',\n  Fatal = 'FATAL',\n  Info = 'INFO',\n  Log = 'LOG',\n  Warn = 'WARN'\n}\n\nexport enum WorkerLoggerLevel {\n  Debug = 'DEBUG',\n  Error = 'ERROR',\n  Fatal = 'FATAL',\n  Info = 'INFO',\n  Trace = 'TRACE',\n  Warn = 'WARN'\n}\n\nexport enum WorkflowJobStatus {\n  ActionRequired = 'ACTION_REQUIRED',\n  Canceled = 'CANCELED',\n  Failure = 'FAILURE',\n  InProgress = 'IN_PROGRESS',\n  New = 'NEW',\n  PendingCancel = 'PENDING_CANCEL',\n  Skipped = 'SKIPPED',\n  Success = 'SUCCESS'\n}\n\nexport enum WorkflowJobType {\n  AppleDeviceRegistrationRequest = 'APPLE_DEVICE_REGISTRATION_REQUEST',\n  Build = 'BUILD',\n  Custom = 'CUSTOM',\n  Deploy = 'DEPLOY',\n  GetBuild = 'GET_BUILD',\n  MaestroTest = 'MAESTRO_TEST',\n  RequireApproval = 'REQUIRE_APPROVAL',\n  Submission = 'SUBMISSION',\n  Update = 'UPDATE'\n}\n\nexport type WorkflowProjectSourceInput = {\n  easJsonBucketKey: Scalars['String'];\n  packageJsonBucketKey?: InputMaybe<Scalars['String']>;\n  projectArchiveBucketKey: Scalars['String'];\n  type: WorkflowProjectSourceType;\n};\n\nexport enum WorkflowProjectSourceType {\n  Gcs = 'GCS'\n}\n\nexport type WorkflowRevisionInput = {\n  fileName: Scalars['String'];\n  yamlConfig: Scalars['String'];\n};\n\nexport type WorkflowRunInput = {\n  projectSource: WorkflowProjectSourceInput;\n};\n\nexport enum WorkflowRunStatus {\n  ActionRequired = 'ACTION_REQUIRED',\n  Canceled = 'CANCELED',\n  Failure = 'FAILURE',\n  InProgress = 'IN_PROGRESS',\n  New = 'NEW',\n  PendingCancel = 'PENDING_CANCEL',\n  Success = 'SUCCESS'\n}\n\nexport enum WorkflowRunTriggerEventType {\n  Github = 'GITHUB',\n  Manual = 'MANUAL'\n}\n\nexport type AppByIdQueryVariables = Exact<{\n  appId: Scalars['String'];\n}>;\n\n\nexport type AppByIdQuery = { __typename?: 'RootQuery', app: { __typename?: 'AppQuery', byId: { __typename?: 'App', id: string, scopeKey: string, ownerAccount: { __typename?: 'Account', id: string, name: string } } } };\n\nexport type CurrentUserQueryVariables = Exact<{ [key: string]: never; }>;\n\n\nexport type CurrentUserQuery = { __typename?: 'RootQuery', meActor?: { __typename: 'Robot', firstName?: string | null, id: string, accounts: Array<{ __typename?: 'Account', id: string, users: Array<{ __typename?: 'UserPermission', permissions: Array<Permission>, actor: { __typename?: 'Robot', id: string } | { __typename?: 'SSOUser', id: string } | { __typename?: 'User', id: string } }> }> } | { __typename: 'SSOUser', username: string, id: string, primaryAccount: { __typename?: 'Account', id: string }, accounts: Array<{ __typename?: 'Account', id: string, users: Array<{ __typename?: 'UserPermission', permissions: Array<Permission>, actor: { __typename?: 'Robot', id: string } | { __typename?: 'SSOUser', id: string } | { __typename?: 'User', id: string } }> }> } | { __typename: 'User', username: string, id: string, primaryAccount: { __typename?: 'Account', id: string }, accounts: Array<{ __typename?: 'Account', id: string, users: Array<{ __typename?: 'UserPermission', permissions: Array<Permission>, actor: { __typename?: 'Robot', id: string } | { __typename?: 'SSOUser', id: string } | { __typename?: 'User', id: string } }> }> } | null };\n\nexport type AppFragment = { __typename?: 'App', id: string, scopeKey: string, ownerAccount: { __typename?: 'Account', id: string, name: string } };\n"], "names": ["AccountAppsSortByField", "AccountUploadSessionType", "ActivityTimelineProjectActivityType", "AndroidBuildType", "AndroidFcmVersion", "AndroidKeystoreType", "AppInternalDistributionBuildPrivacy", "AppPlatform", "AppPrivacy", "AppSort", "AppStoreConnectUserRole", "AppUploadSessionType", "AppleDeviceClass", "AppleTeamType", "A<PERSON><PERSON><PERSON>er", "AssetMetadataStatus", "AuditLogsExportFormat", "AuthProtocolType", "AuthProviderIdentifier", "BackgroundJobResultType", "BackgroundJobState", "BuildCredentialsSource", "BuildIosEnterpriseProvisioning", "BuildLimitThresholdExceededMetadataType", "BuildMode", "BuildPhase", "BuildPriority", "BuildResourceClass", "BuildRetryDisabledReason", "BuildStatus", "BuildTrigger", "BuildWorkflow", "ContinentCode", "CrashSampleFor", "CustomDomainDnsRecordType", "CustomDomainStatus", "DistributionType", "EasBuildBillingResourceClass", "EasBuildDeprecationInfoType", "EasBuildWaiverType", "EasService", "EasServiceMetric", "EasTotalPlanEnablementUnit", "EntityTypeName", "EnvironmentSecretType", "EnvironmentVariableEnvironment", "EnvironmentVariableScope", "EnvironmentVariableVisibility", "Experiment", "Feature", "FingerprintSourceType", "GitHubAppEnvironment", "GitHubAppInstallationStatus", "GitHubBuildTriggerExecutionBehavior", "GitHubBuildTriggerRunStatus", "GitHubBuildTriggerType", "GitHubJobRunJobType", "GitHubJobRunTriggerRunStatus", "GitHubJobRunTriggerType", "InsightsFilterType", "InvoiceDiscountType", "IosBuildType", "IosDistributionType", "IosManagedBuildType", "IosSchemeBuildConfiguration", "JobRunPriority", "JobRunStatus", "MailchimpAudience", "MailchimpTag", "NotificationEvent", "NotificationType", "OfferType", "OnboardingDeviceType", "OnboardingEnvironment", "Order", "Permission", "ProjectArchiveSourceType", "RequestMethod", "RequestsOrderByDirection", "RequestsOrderByField", "ResourceClassExperiment", "ResponseCacheStatus", "ResponseStatusType", "ResponseType", "Role", "SecondFactorMethod", "StandardOffer", "StatuspageIncidentImpact", "StatuspageIncidentStatus", "StatuspageServiceName", "StatuspageServiceStatus", "SubmissionAndroidArchiveType", "SubmissionAndroidReleaseStatus", "SubmissionAndroidTrack", "SubmissionArchiveSourceType", "SubmissionPriority", "SubmissionStatus", "TargetEntityMutationType", "UploadSessionType", "UsageMetricType", "UsageMetricsGranularity", "UserAgentBrowser", "UserAgentOs", "UserEntityTypeName", "WebhookType", "WorkerDeploymentCrashKind", "WorkerDeploymentLogLevel", "WorkerLoggerLevel", "WorkflowJobStatus", "WorkflowJobType", "WorkflowProjectSourceType", "WorkflowRunStatus", "WorkflowRunTriggerEventType"], "mappings": "AAAA;;;;;;CAMC;;;;;;;;;;;IA2BWA,sBAAsB;eAAtBA;;IA4BAC,wBAAwB;eAAxBA;;IAKAC,mCAAmC;eAAnCA;;IAgCAC,gBAAgB;eAAhBA;;IAuBAC,iBAAiB;eAAjBA;;IAgFAC,mBAAmB;eAAnBA;;IAkCAC,mCAAmC;eAAnCA;;IAYAC,WAAW;eAAXA;;IAKAC,UAAU;eAAVA;;IAMAC,OAAO;eAAPA;;IAoBAC,uBAAuB;eAAvBA;;IAmBAC,oBAAoB;eAApBA;;IA0BAC,gBAAgB;eAAhBA;;IAwDAC,aAAa;eAAbA;;IAWAC,UAAU;eAAVA;;IAaAC,mBAAmB;eAAnBA;;IAmBAC,qBAAqB;eAArBA;;IAMAC,gBAAgB;eAAhBA;;IAIAC,sBAAsB;eAAtBA;;IAQAC,uBAAuB;eAAvBA;;IAOAC,kBAAkB;eAAlBA;;IAgCAC,sBAAsB;eAAtBA;;IAmCAC,8BAA8B;eAA9BA;;IAKAC,uCAAuC;eAAvCA;;IAwCAC,SAAS;eAATA;;IAaAC,UAAU;eAAVA;;IA4CAC,aAAa;eAAbA;;IAUAC,kBAAkB;eAAlBA;;IAsBAC,wBAAwB;eAAxBA;;IAQAC,WAAW;eAAXA;;IAUAC,YAAY;eAAZA;;IASAC,aAAa;eAAbA;;IAgBAC,aAAa;eAAbA;;IAWAC,cAAc;eAAdA;;IA8GAC,yBAAyB;eAAzBA;;IAMAC,kBAAkB;eAAlBA;;IAiBAC,gBAAgB;eAAhBA;;IAMAC,4BAA4B;eAA5BA;;IAKAC,2BAA2B;eAA3BA;;IAKAC,kBAAkB;eAAlBA;;IAKAC,UAAU;eAAVA;;IAMAC,gBAAgB;eAAhBA;;IAUAC,0BAA0B;eAA1BA;;IAgBAC,cAAc;eAAdA;;IAyBAC,qBAAqB;eAArBA;;IAKAC,8BAA8B;eAA9BA;;IAMAC,wBAAwB;eAAxBA;;IAKAC,6BAA6B;eAA7BA;;IAMAC,UAAU;eAAVA;;IAIAC,OAAO;eAAPA;;IAyCAC,qBAAqB;eAArBA;;IASAC,oBAAoB;eAApBA;;IAMAC,2BAA2B;eAA3BA;;IAmBAC,mCAAmC;eAAnCA;;IAKAC,2BAA2B;eAA3BA;;IAKAC,sBAAsB;eAAtBA;;IAMAC,mBAAmB;eAAnBA;;IAIAC,4BAA4B;eAA5BA;;IAKAC,uBAAuB;eAAvBA;;IAkBAC,kBAAkB;eAAlBA;;IASAC,mBAAmB;eAAnBA;;IA2BAC,YAAY;eAAZA;;IAkBAC,mBAAmB;eAAnBA;;IAwFAC,mBAAmB;eAAnBA;;IAKAC,2BAA2B;eAA3BA;;IAeAC,cAAc;eAAdA;;IAKAC,YAAY;eAAZA;;IA8BAC,iBAAiB;eAAjBA;;IAOAC,YAAY;eAAZA;;IAOAC,iBAAiB;eAAjBA;;IAiBAC,gBAAgB;eAAhBA;;IAKAC,SAAS;eAATA;;IASAC,oBAAoB;eAApBA;;IAKAC,qBAAqB;eAArBA;;IAKAC,KAAK;eAALA;;IAmBAC,UAAU;eAAVA;;IAgBAC,wBAAwB;eAAxBA;;IAwBAC,aAAa;eAAbA;;IAgCAC,wBAAwB;eAAxBA;;IAKAC,oBAAoB;eAApBA;;IASAC,uBAAuB;eAAvBA;;IAKAC,mBAAmB;eAAnBA;;IAMAC,kBAAkB;eAAlBA;;IAQAC,YAAY;eAAZA;;IAWAC,IAAI;eAAJA;;IAwCAC,kBAAkB;eAAlBA;;IAOAC,aAAa;eAAbA;;IAYAC,wBAAwB;eAAxBA;;IASAC,wBAAwB;eAAxBA;;IAYAC,qBAAqB;eAArBA;;IASAC,uBAAuB;eAAvBA;;IAQAC,4BAA4B;eAA5BA;;IAKAC,8BAA8B;eAA9BA;;IAOAC,sBAAsB;eAAtBA;;IAeAC,2BAA2B;eAA3BA;;IAYAC,kBAAkB;eAAlBA;;IAKAC,gBAAgB;eAAhBA;;IASAC,wBAAwB;eAAxBA;;IAkFAC,iBAAiB;eAAjBA;;IAWAC,eAAe;eAAfA;;IASAC,uBAAuB;eAAvBA;;IAYAC,gBAAgB;eAAhBA;;IAkBAC,WAAW;eAAXA;;IAkCAC,kBAAkB;eAAlBA;;IAyCAC,WAAW;eAAXA;;IAKAC,yBAAyB;eAAzBA;;IASAC,wBAAwB;eAAxBA;;IASAC,iBAAiB;eAAjBA;;IASAC,iBAAiB;eAAjBA;;IAWAC,eAAe;eAAfA;;IAmBAC,yBAAyB;eAAzBA;;IAaAC,iBAAiB;eAAjBA;;IAUAC,2BAA2B;eAA3BA;;;AA7zDL,IAAA,AAAKhH,gDAAAA;;IAEV;;;GAGC;WALSA;;AA4BL,IAAA,AAAKC,kDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,6DAAAA;;;;;;WAAAA;;AAgCL,IAAA,AAAKC,0CAAAA;;;IAGV,sDAAsD;WAH5CA;;AAuBL,IAAA,AAAKC,2CAAAA;;;WAAAA;;AAgFL,IAAA,AAAKC,6CAAAA;;;;WAAAA;;AAkCL,IAAA,AAAKC,6DAAAA;;;WAAAA;;AAYL,IAAA,AAAKC,qCAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,oCAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,iCAAAA;IACV,+BAA+B;IAE/B,+BAA+B;WAHrBA;;AAoBL,IAAA,AAAKC,iDAAAA;;;;;;;;;;;;;;;;;WAAAA;;AAmBL,IAAA,AAAKC,8CAAAA;;WAAAA;;AA0BL,IAAA,AAAKC,0CAAAA;;;;;WAAAA;;AAwDL,IAAA,AAAKC,uCAAAA;;;;WAAAA;;AAWL,IAAA,AAAKC,oCAAAA;IACV,sBAAsB;IAEtB,iBAAiB;WAHPA;;AAaL,IAAA,AAAKC,6CAAAA;;;WAAAA;;AAmBL,IAAA,AAAKC,+CAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,0CAAAA;;WAAAA;;AAIL,IAAA,AAAKC,gDAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKC,iDAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,4CAAAA;;;;;WAAAA;;AAgCL,IAAA,AAAKC,gDAAAA;;;WAAAA;;AAmCL,IAAA,AAAKC,wDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,iEAAAA;;;WAAAA;;AAwCL,IAAA,AAAKC,mCAAAA;;;;;WAAAA;;AAaL,IAAA,AAAKC,oCAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAuCV,oCAAoC;;WAvC1BA;;AA4CL,IAAA,AAAKC,uCAAAA;;;;WAAAA;;AAUL,IAAA,AAAKC,4CAAAA;;;;;IAKV,8DAA8D;IAE9D,+DAA+D;;IAG/D,yCAAyC;IAEzC,yCAAyC;;;;;;;WAZ/BA;;AAsBL,IAAA,AAAKC,kDAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKC,qCAAAA;;;;;;;;WAAAA;;AAUL,IAAA,AAAKC,sCAAAA;;;WAAAA;;AASL,IAAA,AAAKC,uCAAAA;;;;WAAAA;;AAgBL,IAAA,AAAKC,uCAAAA;;;;;;;;;WAAAA;;AAWL,IAAA,AAAKC,wCAAAA;;;WAAAA;;AA8GL,IAAA,AAAKC,mDAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,4CAAAA;;;;;WAAAA;;AAiBL,IAAA,AAAKC,0CAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,sDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,qDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,4CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,oCAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,0CAAAA;;;;;;;;WAAAA;;AAUL,IAAA,AAAKC,oDAAAA;;;;;;;WAAAA;;AAgBL,IAAA,AAAKC,wCAAAA;;;;;;;;;;;;;;;;;;;;;;;WAAAA;;AAyBL,IAAA,AAAKC,+CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,wDAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,kDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,uDAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,oCAAAA;;WAAAA;;AAIL,IAAA,AAAKC,iCAAAA;IACV,oBAAoB;IAEpB,8CAA8C;IAE9C,qBAAqB;IAErB,6BAA6B;WAPnBA;;AAyCL,IAAA,AAAKC,+CAAAA;;WAAAA;;AASL,IAAA,AAAKC,8CAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,qDAAAA;;;;WAAAA;;AAmBL,IAAA,AAAKC,6DAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,qDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,gDAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,6CAAAA;;WAAAA;;AAIL,IAAA,AAAKC,sDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,iDAAAA;;;WAAAA;;AAkBL,IAAA,AAAKC,4CAAAA;;WAAAA;;AASL,IAAA,AAAKC,6CAAAA;;;WAAAA;;AA2BL,IAAA,AAAKC,sCAAAA;;;WAAAA;;AAkBL,IAAA,AAAKC,6CAAAA;;;;;WAAAA;;AAwFL,IAAA,AAAKC,6CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,qDAAAA;;;WAAAA;;AAeL,IAAA,AAAKC,wCAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,sCAAAA;;;;;;;;WAAAA;;AA8BL,IAAA,AAAKC,2CAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,sCAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,2CAAAA;;;;;;;;WAAAA;;AAiBL,IAAA,AAAKC,0CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,mCAAAA;IACV,yCAAyC;IAEzC,uCAAuC;IAEvC,sBAAsB;WALZA;;AASL,IAAA,AAAKC,8CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,+CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,+BAAAA;;;WAAAA;;AAmBL,IAAA,AAAKC,oCAAAA;;;;;WAAAA;;AAgBL,IAAA,AAAKC,kDAAAA;;;;;;WAAAA;;AAwBL,IAAA,AAAKC,uCAAAA;;;;;;;;WAAAA;;AAgCL,IAAA,AAAKC,kDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,8CAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,iDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,6CAAAA;;;;WAAAA;;AAML,IAAA,AAAKC,4CAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKC,sCAAAA;;;;;WAAAA;;AAWL,IAAA,AAAKC,8BAAAA;;;;;;;;WAAAA;;AAwCL,IAAA,AAAKC,4CAAAA;IACV,gCAAgC;IAEhC,QAAQ;WAHEA;;AAOL,IAAA,AAAKC,uCAAAA;IACV,oCAAoC;IAEpC,uBAAuB;IAEvB,oCAAoC;IAEpC,oCAAoC;WAP1BA;;AAYL,IAAA,AAAKC,kDAAAA;;;;;;WAAAA;;AASL,IAAA,AAAKC,kDAAAA;;;;;;;;;WAAAA;;AAYL,IAAA,AAAKC,+CAAAA;;;;;;WAAAA;;AASL,IAAA,AAAKC,iDAAAA;;;;;;WAAAA;;AAQL,IAAA,AAAKC,sDAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,wDAAAA;;;;;WAAAA;;AAOL,IAAA,AAAKC,gDAAAA;;;;;WAAAA;;AAeL,IAAA,AAAKC,qDAAAA;;;;;WAAAA;;AAYL,IAAA,AAAKC,4CAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,0CAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,kDAAAA;;;;WAAAA;;AAkFL,IAAA,AAAKC,2CAAAA;;;IAGV,2DAA2D;IAE3D,wDAAwD;;;WAL9CA;;AAWL,IAAA,AAAKC,yCAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,iDAAAA;;;;;WAAAA;;AAYL,IAAA,AAAKC,0CAAAA;;;;;;;;;;;;;;;;WAAAA;;AAkBL,IAAA,AAAKC,qCAAAA;;;;;;;;WAAAA;;AAkCL,IAAA,AAAKC,4CAAAA;;;;;;;;;;WAAAA;;AAyCL,IAAA,AAAKC,qCAAAA;;;WAAAA;;AAKL,IAAA,AAAKC,mDAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,kDAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,2CAAAA;;;;;;;WAAAA;;AASL,IAAA,AAAKC,2CAAAA;;;;;;;;;WAAAA;;AAWL,IAAA,AAAKC,yCAAAA;;;;;;;;;;WAAAA;;AAmBL,IAAA,AAAKC,mDAAAA;;WAAAA;;AAaL,IAAA,AAAKC,2CAAAA;;;;;;;;WAAAA;;AAUL,IAAA,AAAKC,qDAAAA;;;WAAAA"}