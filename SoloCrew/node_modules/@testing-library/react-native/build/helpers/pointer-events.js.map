{"version": 3, "file": "pointer-events.js", "names": ["_componentTree", "require", "isPointerEventEnabled", "element", "isParent", "parentCondition", "props", "pointerEvents", "hostParent", "getHostParent", "exports"], "sources": ["../../src/helpers/pointer-events.ts"], "sourcesContent": ["import type { ReactTestInstance } from 'react-test-renderer';\n\nimport { getHostParent } from './component-tree';\n\n/**\n * pointerEvents controls whether the View can be the target of touch events.\n * 'auto': The View and its children can be the target of touch events.\n * 'none': The View is never the target of touch events.\n * 'box-none': The View is never the target of touch events but its subviews can be\n * 'box-only': The view can be the target of touch events but its subviews cannot be\n * see the official react native doc https://reactnative.dev/docs/view#pointerevents */\nexport const isPointerEventEnabled = (element: ReactTestInstance, isParent?: boolean): boolean => {\n  const parentCondition = isParent\n    ? element?.props.pointerEvents === 'box-only'\n    : element?.props.pointerEvents === 'box-none';\n\n  if (element?.props.pointerEvents === 'none' || parentCondition) {\n    return false;\n  }\n\n  const hostParent = getHostParent(element);\n  if (!hostParent) return true;\n\n  return isPointerEventEnabled(hostParent, true);\n};\n"], "mappings": ";;;;;;AAEA,IAAAA,cAAA,GAAAC,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMC,qBAAqB,GAAGA,CAACC,OAA0B,EAAEC,QAAkB,KAAc;EAChG,MAAMC,eAAe,GAAGD,QAAQ,GAC5BD,OAAO,EAAEG,KAAK,CAACC,aAAa,KAAK,UAAU,GAC3CJ,OAAO,EAAEG,KAAK,CAACC,aAAa,KAAK,UAAU;EAE/C,IAAIJ,OAAO,EAAEG,KAAK,CAACC,aAAa,KAAK,MAAM,IAAIF,eAAe,EAAE;IAC9D,OAAO,KAAK;EACd;EAEA,MAAMG,UAAU,GAAG,IAAAC,4BAAa,EAACN,OAAO,CAAC;EACzC,IAAI,CAACK,UAAU,EAAE,OAAO,IAAI;EAE5B,OAAON,qBAAqB,CAACM,UAAU,EAAE,IAAI,CAAC;AAChD,CAAC;AAACE,OAAA,CAAAR,qBAAA,GAAAA,qBAAA", "ignoreList": []}