{"version": 3, "file": "format-element.js", "names": ["_prettyFormat", "_interopRequireWildcard", "require", "_mapProps", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "formatElement", "element", "compact", "highlight", "mapProps", "defaultMapProps", "children", "props", "childrenToDisplay", "undefined", "prettyFormat", "$$typeof", "Symbol", "for", "type", "plugins", "ReactTestComponent", "ReactElement", "printFunctionName", "printBasicPrototype", "min", "formatElementList", "elements", "options", "length", "map", "join", "formatJson", "json", "getElementJsonPlugin", "test", "val", "serialize", "config", "indentation", "depth", "refs", "printer", "newVal"], "sources": ["../../src/helpers/format-element.ts"], "sourcesContent": ["import type { ReactTestInstance, ReactTestRendererJSON } from 'react-test-renderer';\nimport type { NewPlugin } from 'pretty-format';\nimport prettyFormat, { plugins } from 'pretty-format';\n\nimport type { MapPropsFunction } from './map-props';\nimport { defaultMapProps } from './map-props';\n\nexport type FormatElementOptions = {\n  /** Minimize used space. */\n  compact?: boolean;\n\n  /** Highlight the output. */\n  highlight?: boolean;\n\n  /** Filter or map props to display. */\n  mapProps?: MapPropsFunction | null;\n};\n\n/***\n * Format given element as a pretty-printed string.\n *\n * @param element Element to format.\n */\nexport function formatElement(\n  element: ReactTestInstance | null,\n  { compact, highlight = true, mapProps = defaultMapProps }: FormatElementOptions = {},\n) {\n  if (element == null) {\n    return '(null)';\n  }\n\n  const { children, ...props } = element.props;\n  const childrenToDisplay = typeof children === 'string' ? [children] : undefined;\n\n  return prettyFormat(\n    {\n      // This prop is needed persuade the prettyFormat that the element is\n      // a ReactTestRendererJSON instance, so it is formatted as JSX.\n      $$typeof: Symbol.for('react.test.json'),\n      type: `${element.type}`,\n      props: mapProps ? mapProps(props) : props,\n      children: childrenToDisplay,\n    },\n    // See: https://www.npmjs.com/package/pretty-format#usage-with-options\n    {\n      plugins: [plugins.ReactTestComponent, plugins.ReactElement],\n      printFunctionName: false,\n      printBasicPrototype: false,\n      highlight: highlight,\n      min: compact,\n    },\n  );\n}\n\nexport function formatElementList(elements: ReactTestInstance[], options?: FormatElementOptions) {\n  if (elements.length === 0) {\n    return '(no elements)';\n  }\n\n  return elements.map((element) => formatElement(element, options)).join('\\n');\n}\n\nexport function formatJson(\n  json: ReactTestRendererJSON | ReactTestRendererJSON[],\n  { compact, highlight = true, mapProps = defaultMapProps }: FormatElementOptions = {},\n) {\n  return prettyFormat(json, {\n    plugins: [getElementJsonPlugin(mapProps), plugins.ReactElement],\n    highlight: highlight,\n    printBasicPrototype: false,\n    min: compact,\n  });\n}\n\nfunction getElementJsonPlugin(mapProps?: MapPropsFunction | null): NewPlugin {\n  return {\n    test: (val) => plugins.ReactTestComponent.test(val),\n    serialize: (val, config, indentation, depth, refs, printer) => {\n      let newVal = val;\n      if (mapProps && val.props) {\n        newVal = { ...val, props: mapProps(val.props) };\n      }\n      return plugins.ReactTestComponent.serialize(\n        newVal,\n        config,\n        indentation,\n        depth,\n        refs,\n        printer,\n      );\n    },\n  };\n}\n"], "mappings": ";;;;;;;;AAEA,IAAAA,aAAA,GAAAC,uBAAA,CAAAC,OAAA;AAGA,IAAAC,SAAA,GAAAD,OAAA;AAA8C,SAAAE,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAJ,wBAAAI,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAa9C;AACA;AACA;AACA;AACA;AACO,SAASW,aAAaA,CAC3BC,OAAiC,EACjC;EAAEC,OAAO;EAAEC,SAAS,GAAG,IAAI;EAAEC,QAAQ,GAAGC;AAAsC,CAAC,GAAG,CAAC,CAAC,EACpF;EACA,IAAIJ,OAAO,IAAI,IAAI,EAAE;IACnB,OAAO,QAAQ;EACjB;EAEA,MAAM;IAAEK,QAAQ;IAAE,GAAGC;EAAM,CAAC,GAAGN,OAAO,CAACM,KAAK;EAC5C,MAAMC,iBAAiB,GAAG,OAAOF,QAAQ,KAAK,QAAQ,GAAG,CAACA,QAAQ,CAAC,GAAGG,SAAS;EAE/E,OAAO,IAAAC,qBAAY,EACjB;IACE;IACA;IACAC,QAAQ,EAAEC,MAAM,CAACC,GAAG,CAAC,iBAAiB,CAAC;IACvCC,IAAI,EAAE,GAAGb,OAAO,CAACa,IAAI,EAAE;IACvBP,KAAK,EAAEH,QAAQ,GAAGA,QAAQ,CAACG,KAAK,CAAC,GAAGA,KAAK;IACzCD,QAAQ,EAAEE;EACZ,CAAC;EACD;EACA;IACEO,OAAO,EAAE,CAACA,qBAAO,CAACC,kBAAkB,EAAED,qBAAO,CAACE,YAAY,CAAC;IAC3DC,iBAAiB,EAAE,KAAK;IACxBC,mBAAmB,EAAE,KAAK;IAC1BhB,SAAS,EAAEA,SAAS;IACpBiB,GAAG,EAAElB;EACP,CACF,CAAC;AACH;AAEO,SAASmB,iBAAiBA,CAACC,QAA6B,EAAEC,OAA8B,EAAE;EAC/F,IAAID,QAAQ,CAACE,MAAM,KAAK,CAAC,EAAE;IACzB,OAAO,eAAe;EACxB;EAEA,OAAOF,QAAQ,CAACG,GAAG,CAAExB,OAAO,IAAKD,aAAa,CAACC,OAAO,EAAEsB,OAAO,CAAC,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC;AAC9E;AAEO,SAASC,UAAUA,CACxBC,IAAqD,EACrD;EAAE1B,OAAO;EAAEC,SAAS,GAAG,IAAI;EAAEC,QAAQ,GAAGC;AAAsC,CAAC,GAAG,CAAC,CAAC,EACpF;EACA,OAAO,IAAAK,qBAAY,EAACkB,IAAI,EAAE;IACxBb,OAAO,EAAE,CAACc,oBAAoB,CAACzB,QAAQ,CAAC,EAAEW,qBAAO,CAACE,YAAY,CAAC;IAC/Dd,SAAS,EAAEA,SAAS;IACpBgB,mBAAmB,EAAE,KAAK;IAC1BC,GAAG,EAAElB;EACP,CAAC,CAAC;AACJ;AAEA,SAAS2B,oBAAoBA,CAACzB,QAAkC,EAAa;EAC3E,OAAO;IACL0B,IAAI,EAAGC,GAAG,IAAKhB,qBAAO,CAACC,kBAAkB,CAACc,IAAI,CAACC,GAAG,CAAC;IACnDC,SAAS,EAAEA,CAACD,GAAG,EAAEE,MAAM,EAAEC,WAAW,EAAEC,KAAK,EAAEC,IAAI,EAAEC,OAAO,KAAK;MAC7D,IAAIC,MAAM,GAAGP,GAAG;MAChB,IAAI3B,QAAQ,IAAI2B,GAAG,CAACxB,KAAK,EAAE;QACzB+B,MAAM,GAAG;UAAE,GAAGP,GAAG;UAAExB,KAAK,EAAEH,QAAQ,CAAC2B,GAAG,CAACxB,KAAK;QAAE,CAAC;MACjD;MACA,OAAOQ,qBAAO,CAACC,kBAAkB,CAACgB,SAAS,CACzCM,MAAM,EACNL,MAAM,EACNC,WAAW,EACXC,KAAK,EACLC,IAAI,EACJC,OACF,CAAC;IACH;EACF,CAAC;AACH", "ignoreList": []}