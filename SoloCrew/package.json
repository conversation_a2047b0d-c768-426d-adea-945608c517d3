{"name": "solocrew", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-native-picker/picker": "^2.11.1", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@types/react": "^19.1.8", "@types/react-native": "^0.72.8", "axios": "^1.11.0", "expo": "~53.0.20", "expo-haptics": "^14.1.4", "expo-notifications": "^0.31.4", "expo-status-bar": "~2.2.3", "lottie-react-native": "^7.2.4", "nativewind": "^4.1.23", "react": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.2", "react-native-reanimated": "^4.0.0", "react-native-safe-area-context": "^5.5.2", "react-native-screens": "^4.13.1", "react-native-vector-icons": "^10.3.0", "tailwindcss": "^3.4.17", "typescript": "^5.8.3", "zustand": "^5.0.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.2.0", "@types/react-native-vector-icons": "^6.4.18", "jest": "^29.7.0"}, "private": true}